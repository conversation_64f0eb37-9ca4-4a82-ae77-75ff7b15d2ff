# frozen_string_literal: true

module Api
  class AccountsController < ApiController
    # TODO: This controller does not appear to be used
    include Oauthable

    AUTHORIZED_APPS = [
      ExternalApp::ABOVELENDING_FRONTEND_CLIENT,
      ExternalApp::INTERNAL_APP
    ].freeze

    before_action -> { authorize_oauth(AUTHORIZED_APPS) }

    # GET /api/accounts/by_email
    def find_by_email
      borrower = Borrower.find_by(email: params[:email]&.to_s&.downcase)
      loan = ::Loan.latest_active_for_borrower(borrower).first if borrower.present?
      lead = Lead.find_by(email: borrower.email) if borrower.present?

      if borrower.present?
        render_account_info(borrower:, loan:, lead:)
      else
        render json: {}, status: :not_found
      end
    end

    # GET /api/accounts/by_identity_id
    def find_by_identity_id
      borrower = Borrower.find_by(identity_id: params[:identity_id])
      loan = ::Loan.latest_active_for_borrower(borrower).first if borrower.present?
      lead = Lead.find_by(email: borrower.email) if borrower.present?

      if borrower.present?
        render_account_info(borrower:, loan:, lead:)
      else
        render json: {}, status: :not_found
      end
    end

    def render_account_info(borrower:, loan:, lead:)
      render json: {
        'borrower' => borrower_details(borrower:),
        'loan' => loan_details(loan:),
        'lead' => { 'code_status' => lead&.code_status }
      }.compact_blank
    end

    def borrower_details(borrower:)
      return unless borrower.present?

      borrower.attributes.slice('id', 'identity_id', 'email', 'first_name', 'last_name').compact_blank
    end

    def loan_details(loan:)
      return unless loan.present?

      loan.attributes.slice('id', 'product_type', 'loan_app_status')
      {
        'id' => loan.id,
        'product_type' => loan.product_type,
        'loan_app_status' => loan.loan_app_status.name,
        'is_active' => loan.active?
      }.compact_blank
    end
  end
end
