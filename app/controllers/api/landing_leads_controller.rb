# frozen_string_literal: true

module Api
  class LandingLeadsController < ApiController # rubocop:disable Metrics/ClassLength
    EMAIL_VALIDATION_SOURCE = 'intake lander'
    LEAD_CODE_PARAM = 'offer'
    SERVICE_ENTITY_PARAM = 's'
    PARAM_TO_SERVICE_ENTITY = {
      'bf' => Constants::ServiceEntityNames::BEYOND_FINANCE,
      'fllg' => Constants::ServiceEntityNames::FIVE_LAKES_LAW_GROUP
    }.freeze

    before_action :set_event_agent_user

    # POST /landing_leads
    # Used with Integration tests
    def create
      @landing_lead = LandingLead.new(landing_lead_attrs)

      if landing_lead.save
        withdraw_loan if withdraw_loan?

        status = email_valid? ? :created : :precondition_required
        render json: account_details, status:
      else
        render json: { errors: landing_lead.errors }, status: :bad_request
      end
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta:)
    end

    private

    attr_reader :landing_lead

    # The key goal is to ensure that we can determine exactly what went wrong without needing
    # to reconstruct information from local sources such as databases or logs.
    def meta
      {
        agent: @event_agent,
        landing_lead_id: landing_lead&.id,
        lead_code: extract_code_from_url,
        is_eligible: eligible?,
        lead_id: lead&.id,
        loan_id: loan&.id,
        is_loan_active: loan_is_active?,
        borrower_id: borrower&.id,
        is_activated_account: activated_account?,
        is_loan_present: loan.present?,
        is_basic_info_complete_status: loan&.basic_info_complete?,
        is_add_info_complete_status: loan&.add_info_complete?,
        is_withdraw_loan: withdraw_loan?,
        is_email_validation_enabled: email_validation_enabled?,
        is_email_valid: email_valid?,
        email_verdict:,
        email_score:
      }
    end

    def eligible?
      lead&.code_status == 'valid'
    end

    def account_details
      code_status = lead&.code_status

      {
        id: landing_lead.id,
        borrower: borrower_details,
        loan: loan_details,
        lead: { code_status: }.compact,
        email_verdict:
      }.compact
    end

    def borrower
      @borrower ||= Borrower
                    .find_by(Borrower.arel_table[:email].matches(landing_lead.email))
    end

    def loan
      @loan ||= ::Loan.latest_active_for_borrower(borrower).first if borrower.present?
    end

    def lead
      @lead ||= Lead.with_code(landing_lead.lead_code).first
    end

    def borrower_details
      return unless borrower.present?

      @borrower_details ||= borrower.attributes
                                    .slice('id', 'identity_id', 'email', 'first_name', 'last_name')
                                    .merge!(activated_account: activated_account?)
                                    .symbolize_keys.compact
    end

    def activated_account?
      return @activated_account if defined?(@activated_account)

      @activated_account = landing_lead.email && Users::CheckAccountActivation.call(email: landing_lead.email)
    end

    def loan_is_active?
      return @loan_is_active if defined?(@loan_is_active)
      return @loan_is_active = false unless loan.present?

      @loan_is_active = ::LoanAppStatus::ACTIVE_STATUS
                        .include?(loan.loan_app_status.name)
    end

    def loan_details
      return unless loan.present?

      {
        id: loan.id,
        product_type: loan.product_type,
        loan_app_status: loan.loan_app_status.name,
        is_active: loan_is_active?
      }.compact
    end

    def landing_lead_params
      params.permit(:first_name, :last_name, :phone_number, :email, :url)
    end

    def landing_lead_attrs
      landing_lead_params
        .compact
        .tap do |landing_lead|
          landing_lead[:tcpa_accepted_at] = accepted_at if params[:tcpa_accepted]
          landing_lead[:privacy_accepted_at] = accepted_at if params[:privacy_accepted]
          landing_lead[:lead_code] = extract_code_from_url
          landing_lead[:service_entity_name] = extract_service_entity_from_url
        end
    end

    def accepted_at
      return @accepted_at if defined? @accepted_at

      @accepted_at = Time.zone.now
    end

    def extract_service_entity_from_url
      service_entity_param = parsed_url_query[SERVICE_ENTITY_PARAM]&.first&.downcase
      PARAM_TO_SERVICE_ENTITY[service_entity_param]
    end

    def extract_code_from_url
      parsed_url_query[LEAD_CODE_PARAM]&.first
    end

    def parsed_url_query
      return @parsed_url_query if defined? @parsed_url_query

      uri = URI.parse(params[:url])
      @parsed_url_query = CGI.parse(uri.query || '')
    rescue URI::InvalidURIError
      @parsed_url_query = {}
    end

    def withdraw_loan?
      return false unless loan.present?
      return false if activated_account?
      return false if loan.approved?
      return false unless loan.basic_info_complete? || loan.add_info_complete?

      true
    end

    def email_validation_enabled?
      return @email_validation_enabled if defined? @email_validation_enabled

      @email_validation_enabled = Flipper.enabled?(:enable_email_validation)
    end

    def email_valid?
      return true unless email_validation_enabled?

      email_validate.valid?
    end

    def email_verdict
      return nil if email_valid?

      email_validate.verdict
    end

    def email_score
      return nil if email_valid?

      email_validate.score
    end

    def email_validate
      @email_validate ||= Sendgrid::EmailValidate
                          .call(email: params[:email], source: EMAIL_VALIDATION_SOURCE)
    end

    # Withdraws non-activated accounts that are not in OFFERED+ statuses
    def withdraw_loan
      Loans::WithdrawJob.perform_async(loan.id)
    end
  end
end
