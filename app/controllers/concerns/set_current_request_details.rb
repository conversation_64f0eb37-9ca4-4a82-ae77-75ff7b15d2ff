# frozen_string_literal: true

module SetCurrentRequestDetails
  extend ActiveSupport::Concern
  include TokenHelper
  include RequestHelper
  include ServiceObjectHelper

  included do
    before_action do
      Current.ip_address = forwarded_for_ip_address(default_to_remote_ip: true)
      Current.oauth_token = extract_token_from_headers(request.headers)
      Current.path = request.path
      Current.service_object_name = ams_service_object_name

      # TODO: Update to use RequestTagging.new(request).attributes or similar
      # functionality to populate ObservabilityAttributes when cleaning up named_tags
      # in logs.
      attributes = Rails.logger.named_tags
      ObservabilityAttributes.reset_attributes!(attributes)
    end
  end
end
