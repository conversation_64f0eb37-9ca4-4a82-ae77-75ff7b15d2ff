# frozen_string_literal: true

module Loanpro
  class CancelPayment < Service::Base
    class CancelPaymentSystemError < StandardError; end
    attribute :borrower, presence: true
    attribute :autopay_id, presence: true

    def call
      validate!
      validate_loan
      Clients::LoanproApi.cancel_autopay(auto_pay['loanId'], autopay_id)
    rescue StandardError => e
      Rails.logger.error('Error in CancelPayment', autopay_id:, borrower_id: borrower.id, error_message: e.message)
      raise CancelPaymentSystemError, "cancel payment error - #{e.message}"
    end

    private

    def auto_pay
      @auto_pay ||= Clients::LoanproApi.get_autopay(autopay_id)

      raise CancelPaymentSystemError, "Autopay record not found for ID: #{autopay_id}" unless @auto_pay.present?

      unless @auto_pay['type'] == 'autopay.type.single'
        raise CancelPaymentSystemError,
              "Autopay ID #{autopay_id} cannot be cancelled. Only 'autopay.type.single' type autopays are cancellable."
      end

      @auto_pay
    end

    def validate_loan
      loanpro_loan_id = auto_pay['loanId']
      response ||= Clients::LoanproApi.get_loan(loanpro_loan_id)
      return unless borrower.loan&.unified_id != response['displayId']

      raise(CancelPaymentSystemError, "Loan not found for borrower #{borrower.id}")
    end
  end
end
