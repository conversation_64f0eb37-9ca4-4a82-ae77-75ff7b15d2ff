# frozen_string_literal: true

module Loanpro
  class CreatePayment < Service::Base
    include ActiveModel::Validations
    include Notifier

    class CreatePaymentSystemError < StandardError; end
    class CreatePaymentValidationError < StandardError; end

    attribute :loanpro_loan_id, presence: true
    attribute :amount, presence: true
    attribute :apply_date, :date
    attribute :payment_profile_id, presence: true
    attribute :apply_timezone, presence: true
    attribute :charge_off_recovery, presence: true

    validate :validate_time_zone
    validate :minimum_apply_date

    def call
      validate!
      calculate_process_date
      create_cip_autopay
      Servicing::SendPaymentScheduledEmailJob.perform_async(loanpro_loan_id, amount.to_s, apply_date.strftime('%F'))
      notify_async_event(name: event_name, success: true, meta:)
      true
    rescue StandardError => e
      notify_async_event(name: event_name, success: false, fail_reason: e.message, meta:)
      raise CreatePaymentSystemError, e.message
    end

    private

    def create_cip_autopay
      autopay_attributes = PaymentHelper.default_autopay_attributes.merge(payment_attributes)
      Clients::LoanproApi.create_cip_autopay(loanpro_loan_id, autopay_attributes)
    end

    def minimum_apply_date
      Time.use_zone(@time_zone) do
        if apply_date < Date.current - 1
          Rails.logger.error("Apply date: #{apply_date} for create payment is incorrect", loanpro_loan_id:)
          errors.add(:apply_date, :invalid)
        end
      end
    end

    def calculate_process_date
      @process_date =
        if apply_date.to_date <= CentralTimeZone.today && CentralTimeZone.now >= CentralTimeZone.at_2pm
          CentralTimeZone.today + 1
        elsif apply_date.to_date < CentralTimeZone.today && CentralTimeZone.now < CentralTimeZone.at_2pm
          CentralTimeZone.today
        else
          apply_date.to_date
        end

      @process_date = @process_date.strftime('%Y-%m-%d')
    end

    def validate_time_zone
      @time_zone = PaymentHelper.time_zone(apply_timezone)
      return unless @time_zone.blank? || ActiveSupport::TimeZone[@time_zone].nil?

      Rails.logger.error("No valid timezone found for #{apply_timezone}", loanpro_loan_id:)
    end

    def payment_attributes
      {
        amount: amount.to_d,
        applyDate: apply_date.strftime('%F'),
        processDate: @process_date,
        processDateTime: "#{@process_date} #{CentralTimeZone.three_pm_utc_hour}:00:00",
        primaryPaymentMethodId: payment_profile_id,
        **charge_off_payload
      }
    end

    def charge_off_payload
      return {} unless charge_off_recovery

      {
        chargeOffRecovery: 1,
        payoffAdjustment: PaymentHelper::PROCEED_WITH_PAYMENT,
        processZeroOrNegativeBalance: true
      }
    end

    def meta
      { loanpro_loan_id:, amount:, apply_date:, process_date: @process_date,
        payment_profile_id:, apply_timezone:, charge_off_recovery: }
    end
  end
end
