# frozen_string_literal: true

module Loanpro
  class DashboardDetails < Service::Base
    class DashboardDetailsSystemError < StandardError; end

    attribute :borrower, presence: true
    attribute :loan_id, presence: true

    def call
      validate!

      Clients::DashServicingApi::DashboardDetails.new(build_payload)
    rescue StandardError => e
      Rails.logger.error('Error in DashboardDetails', error_message: e.message)
      raise DashboardDetailsSystemError, "dashboard details error - #{e.message}"
    end

    private

    def loan_details
      @loan_details ||= begin
        expands = %w[LoanSetup StatusArchive Transactions SubPortfolios]
        Clients::LoanproApi.fetch_loan_details(loan_id, expands)
      end
    end

    def status_archive
      @status_archive ||= loan_details.dig('StatusArchive', 0)
    end

    def loan_setup
      @loan_setup ||= loan_details['LoanSetup']
    end

    def build_payload
      {
        'loan_status_text' => status_archive['loanStatusText'],
        'number_of_remaining_terms' => status_archive['periodsRemaining'],
        'days_past_due' => status_archive['daysPastDue'],
        'sub_status' => LoanproHelper.loan_sub_status(status_archive['loanSubStatusId'],
                                                      status_archive['daysPastDue']),
        'sub_status_id' => status_archive['loanSubStatusId'],
        'debt_sale' => status_archive['loanSubStatusId'] == LoanproHelper::LOAN_CLOSED_DEBT_SALE,
        'beneficial_owner_name' => beneficial_owner_name,
        'beneficial_owner_details' => beneficial_owner_details,
        **build_contract_payload,
        **build_payments_payload,
        **build_borrower_payload
      }
    end

    def build_contract_payload
      {
        'apr' => loan_setup['apr'].to_s,
        'loan_amount' => loan_setup['loanAmount'].to_f,
        'underwriting' => loan_setup['underwriting'].to_f,
        'contract_date' => LoanproHelper.parse_date(loan_setup['contractDate']).to_s,
        'number_of_terms' => LoanproHelper.parse_float(loan_setup['loanTerm']).to_i,
        'remaining_balance' => remaining_balance
      }
    end

    def build_payments_payload
      {
        'loan_payment' => LoanproHelper.parse_payment_schedule(loan_setup).dig(0, 'payment').to_f,
        'current_due_date' => LoanproHelper.parse_date(status_archive['nextPaymentDate']).to_s,
        'current_payment_due' => status_archive['amountDue'],
        'next_payment_amount' => status_archive['nextPaymentAmount'],
        'next_payment_date' => LoanproHelper.parse_date(status_archive['nextPaymentDate']).to_s,
        'overdue_amount' => status_archive['amountDue'],
        'payment_frequency' => loan_setup['paymentFrequency'],
        'last_payment' => last_payment
      }
    end

    def build_borrower_payload
      {
        'borrower_name' => borrower.full_name,
        'address' => borrower.latest_borrower_info.address,
        'city' => borrower.latest_borrower_info.city,
        'state' => borrower.latest_borrower_info.state,
        'zip_code' => borrower.latest_borrower_info.zip_code
      }
    end

    def remaining_balance
      if status_archive['loanSubStatusText'] == LoanproHelper::LOAN_SUB_STATUSES[:charged_off]
        status_archive['netChargeOff']
      else
        status_archive['payoff']
      end
    end

    def last_payment
      last_payment = loan_details['Transactions'].lazy.select do |transaction|
        transaction['type'] == 'payment'
      end

      last_payment = last_payment.max_by do |transaction|
        LoanproHelper.parse_date(transaction['date'])
      end

      return {} unless last_payment

      {
        'payment_amount' => last_payment['paymentAmount'],
        'date' => LoanproHelper.parse_date(last_payment['date']).iso8601,
        'payment_principal' => last_payment['paymentPrincipal'],
        'payment_interest' => last_payment['paymentInterest']
      }
    end

    def beneficial_owner_name
      titles = loan_details['SubPortfolios']&.pluck('title') || []
      # TODO: Migrate this to use beneficial owner records instead of a constant
      (titles & LoanproHelper::DEBT_SALE_BENEFICIAL_NAME).first
    end

    def beneficial_owner_details
      return nil unless status_archive['loanSubStatusId'] == LoanproHelper::LOAN_CLOSED_DEBT_SALE

      @beneficial_owner_details ||= Clients::DashServicingApi.beneficiary_owner_details(
        borrower: borrower,
        unified_id: borrower.loan&.unified_id
      )
    end
  end
end
