# frozen_string_literal: true

# Clones non-rejected documents from the previous todo.todo_docs of a loan to the newly
# created todo, typically triggered when a new offer is selected. This process prevents
# the need for re-uploading documents by cloning the last set of todo documents that were not
# rejected. And it updates the status of the current todo to 'review' to
# reflect that these documents are pending review. This ensures necessary documentation
# is carried forward efficiently and ready for subsequent processing steps.
module Gds
  class CloneTodoDocs < Service::Base
    attribute :loan, type_for(::Loan)
    attribute :type, :string

    TODO_DOC_ATTRIBUTES = %w[name url mime_type s3_bucket s3_key].freeze

    def call
      validate!
      return if current_todo&.todo_docs.nil? || current_todo.todo_docs.any?
      return if previous_todo.nil? || previous_todo_docs.none?

      clone_todo_docs
      send_documents_to_gds
    end

    def clone_todo_docs
      Rails.logger.info("Cloning todo doc for #{loan.id} for #{type}",
                        loan_id: loan.id, type:, document_count: previous_todo_docs.count)

      ApplicationRecord.transaction do
        previous_todo_docs.each do |doc|
          build_todo_doc(doc)
        end

        current_todo.save!
        current_todo.update!(status: Todo.statuses[:review])
      end
    end

    private

    def todos
      return @todos if defined? @todos

      @todos = loan.reload.todos.where(type:).reorder(created_at: :desc).limit(2)
    end

    def previous_todo_docs
      previous_todo.todo_docs.reject { |d| d.status == TodoDoc.statuses[:rejected] }
    end

    def previous_todo
      todos.last
    end

    def current_todo
      todos.first
    end

    def build_todo_doc(doc)
      attributes = doc.slice(*TODO_DOC_ATTRIBUTES)
                      .merge(id: SecureRandom.uuid,
                             status: TodoDoc.statuses[:pending])

      Rails.logger.info("Cloning todo doc for #{loan.id} for #{type}",
                        loan_id: loan.id,
                        previous_todo_id: doc.id,
                        todo_id: attributes[:id],
                        name: doc.name)

      current_todo.todo_docs.build(attributes)
    end

    def create_gds_request
      gds_documents = current_todo.todo_docs.map do |doc|
        Clients::GdsApi::Document.new(id: doc[:id], name: doc[:name], url: doc[:url], mime_type: doc[:mime_type])
      end

      {
        request_id: loan.request_id,
        task_id: current_todo.external_id,
        documents: gds_documents,
        product_type: loan.product_type,
        loan_app: Clients::GdsApi::LoanApplication.new(app_status: 'PENDING')
      }
    end

    def send_documents_to_gds
      gds_request = create_gds_request
      response = Clients::GdsApi.submit_documents(**gds_request)

      raise Ams::ServiceObject::ThirdPartyNotWorking, response['error_message'] if response['error_message']
    end
  end
end
