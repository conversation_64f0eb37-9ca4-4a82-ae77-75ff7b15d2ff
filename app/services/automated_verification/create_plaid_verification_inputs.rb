# frozen_string_literal: true

module AutomatedVerification
  class CreatePlaidVerificationInputs < Service::Base # rubocop:disable Metrics/ClassLength
    include Notifier

    PLAID_TRANSACTION_BANK_PENALTIES_CATEGORY = 'BANK_PENALTIES'
    PLAID_TRANSACTION_NSF_SUBCATEGORY = 'BANK_PENALTIES_INSUFFICIENT_AND_LATE_FEES'
    PLAID_TRANSACTION_OVERDRAFT_SUBCATEGORY = 'BANK_PENALTIES_CASH_ADVANCE_AND_OVERDRAFT_FEES'

    attribute :loan, type_for(::Loan)
    attribute :bank_account, type_for(BankAccount)
    attribute :bank_todo, type_for(Todo)
    attribute :plaid_reports # Array of PlaidReport records

    def call
      Plaid::AttachAssetReportPdf.call(verification_inputs:, bank_account:, bank_todo:, plaid_reports:)
      capture_verification_inputs_audit
      verification_inputs
    end

    private

    def verification_inputs
      @verification_inputs ||= VerificationInputs.create!(
        loan:,
        source: :plaid,
        matching_account:,
        bank_statement_end_date: extract_bank_statement_end_date,
        bank_account_balance_current: extract_bank_account_balance_current,
        bank_account_balance_30_days_ago: extract_bank_account_balance_30_days_ago,
        nsfs_30_days: count_transactions_past_30_days(PLAID_TRANSACTION_BANK_PENALTIES_CATEGORY,
                                                      PLAID_TRANSACTION_NSF_SUBCATEGORY),
        overdrafts_30_days: count_transactions_past_30_days(PLAID_TRANSACTION_BANK_PENALTIES_CATEGORY,
                                                            PLAID_TRANSACTION_OVERDRAFT_SUBCATEGORY),
        nsf_count_by_description: count_nsf_count_by_description,
        negative_daily_balances_30_days: count_negative_daily_balances_in_past_30_days,
        personal_loan_deposits: personal_loan_deposits?,
        high_cost_payday_cash_advance_deposits: high_cost_payday_cash_advance_deposits?,
        high_cost_payday_cash_advance_payments: high_cost_payday_cash_advance_payments?,
        verification_version_id:,
        credit_model_level:
      )
    end

    def matching_account
      return nil if bank_account.blank?

      { account_number: bank_account.last_four_account_number }
    end

    def extract_bank_statement_end_date
      return nil if assets_plaid_report.blank?

      plaid_report_generation_date
    end

    def extract_bank_account_balance_current
      return nil if assets_plaid_report.blank?

      assets_plaid_report.response.dig('report', 'items', 0, 'accounts', 0, 'balances', 'current') ||
        assets_plaid_report.response.dig('report', 'items', 0, 'accounts', 0, 'balances', 'available')
    end

    def extract_bank_account_balance_30_days_ago
      return nil if assets_plaid_report.blank?

      historical_balances = assets_plaid_report.response.dig('report', 'items', 0, 'accounts', 0,
                                                             'historical_balances')
      return nil if historical_balances.blank?

      # Plaid Asset Report historical balances are daily ending balances. We want to use the starting balance from
      # 30 days ago, so we need to pull the historical balance from 31 days ago to get this.
      date_31_days_ago = plaid_report_generation_date - 31.days
      starting_balance_entry_30_days_ago = historical_balances.detect do |balance_entry|
        balance_entry['date'] == date_31_days_ago.iso8601
      end
      return nil if starting_balance_entry_30_days_ago.blank?

      starting_balance_entry_30_days_ago['current']
    end

    def count_transactions_past_30_days(category, subcategory)
      return nil if assets_plaid_report.blank?

      transaction_details = assets_plaid_report.response.dig('report', 'items', 0, 'accounts', 0,
                                                             'transactions')
      return nil if transaction_details.blank?

      transaction_details.filter do |transaction_data|
        in_past_30_days?(transaction_data['date']) &&
          transaction_data.dig('credit_category', 'primary') == category &&
          transaction_data.dig('credit_category', 'detailed') == subcategory
      end.length
    end

    def count_nsf_count_by_description
      return nil if assets_plaid_report.blank?

      nsf_transactions_by_description.length
    end

    def count_negative_daily_balances_in_past_30_days
      return nil if assets_plaid_report.blank?

      historical_balances = assets_plaid_report.response.dig('report', 'items', 0, 'accounts', 0,
                                                             'historical_balances')
      return nil if historical_balances.blank?

      historical_balances.count do |balance_entry|
        balance_entry['current'].present? &&
          balance_entry['current'].negative? &&
          in_past_30_days?(balance_entry['date'])
      end
    end

    def personal_loan_deposits?
      return true if knockout_transactions[:personal_loan_deposits]&.present?

      false
    end

    def high_cost_payday_cash_advance_deposits?
      return true if knockout_transactions[:high_cost_payday_deposits]&.present?

      false
    end

    def high_cost_payday_cash_advance_payments?
      return true if knockout_transactions[:high_cost_payday_payments]&.present?

      false
    end

    def knockout_transactions
      return @knockout_transactions if defined? @knockout_transactions
      return @knockout_transactions = {} if assets_plaid_report.blank?

      @knockout_transactions =
        AutomatedVerification::IdentifyKnockoutTransactions.call(report: assets_plaid_report,
                                                                 bank_statement_end_date: plaid_report_generation_date)
    end

    def nsf_transactions_by_description
      @nsf_transactions_by_description ||=
        if assets_plaid_report.blank?
          []
        else
          AutomatedVerification::IdentifyNsfTransactions.call(report: assets_plaid_report,
                                                              bank_statement_end_date: plaid_report_generation_date)
        end
    end

    def capture_verification_inputs_audit
      notify_audit_event(name: 'verification_inputs', success: true, meta: verification_inputs_audit_meta)
    end

    def verification_inputs_audit_meta
      {
        verification_version_id:,
        source: :plaid,
        assets_report_id: assets_plaid_report&.id,
        personal_loan_deposit_ids: knockout_transactions[:personal_loan_deposits]&.to_json,
        high_cost_payday_cash_advance_deposit_ids: knockout_transactions[:high_cost_payday_deposits]&.to_json,
        high_cost_payday_cash_advance_payment_ids: knockout_transactions[:high_cost_payday_payments]&.to_json,
        nsf_transactions_by_description: nsf_transactions_by_description.to_json,
        experiment_cohort:,
        credit_model_level:
      }
    end

    def plaid_report_generation_date
      @plaid_report_generation_date ||= DateHelper.time_in_ct(assets_plaid_report.created_at).to_date
    end

    def in_past_30_days?(date_string)
      date_string.present? && date_string.to_date >= (plaid_report_generation_date - 30.days)
    rescue Date::Error => e
      Rails.logger.error('Unable to parse Plaid transaction date.', error_message: e.message, date_string:)
      false
    end

    def assets_plaid_report
      @assets_plaid_report ||= plaid_reports.detect do |plaid_report|
        plaid_report.report_type == PlaidReport::ASSETS_REPORT_TYPE
      end
    end

    def verification_version_id
      @verification_version_id ||= GetVerificationVersion.call
    end

    def experiment_cohort
      @experiment_cohort ||= Experiment['2025_04_CHI_1753_Credit_Model_1_0'].fetch_cohort_for(loan.borrower)
    end

    def credit_model_challenger?
      return false unless Flipper.enabled?(:experiment_2025_04_CHI_1753_Credit_Model_1_0)

      experiment_cohort == 'challenger'
    end

    def credit_model_level
      return 'n/a' unless Flipper.enabled?(:experiment_2025_04_CHI_1753_Credit_Model_1_0)
      return 'n/a' unless credit_model_challenger?

      loan.loan_detail&.credit_model_level || 'unavailable'
    end
  end
end
