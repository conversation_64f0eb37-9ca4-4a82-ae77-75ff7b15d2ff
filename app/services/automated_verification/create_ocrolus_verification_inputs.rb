# frozen_string_literal: true

module AutomatedVerification
  class CreateOcrolusVerificationInputs < Service::Base # rubocop:disable Metrics/ClassLength
    include Notifier

    OCROLUS_ENRICHED_TRANSACTIONS_NSF_KEY = 'nsf'
    OCROLUS_ENRICHED_TRANSACTIONS_OVERDRAFT_KEY = 'overdraft'

    attribute :loan, type_for(::Loan)
    attribute :bank_account, type_for(BankAccount)
    attribute :bank_todo, type_for(Todo)
    attribute :ocrolus_reports # Array of OcrolusReport records

    def call
      update_todo_doc_statuses
      capture_verification_inputs_audit
      verification_inputs
    end

    private

    def verification_inputs
      @verification_inputs ||= VerificationInputs.create!(
        loan:,
        source: :ocrolus,
        matching_account: extract_matching_account,
        bank_statement_end_date:,
        bank_account_balance_current: extract_bank_account_balance_current,
        bank_account_balance_30_days_ago: extract_bank_account_balance_30_days_ago,
        nsfs_30_days: count_transactions_past_30_days(OCROLUS_ENRICHED_TRANSACTIONS_NSF_KEY),
        overdrafts_30_days: count_transactions_past_30_days(OCROLUS_ENRICHED_TRANSACTIONS_OVERDRAFT_KEY),
        nsf_count_by_description: count_nsf_count_by_description,
        negative_daily_balances_30_days: count_negative_daily_balances_in_past_30_days,
        personal_loan_deposits: personal_loan_deposits?,
        high_cost_payday_cash_advance_deposits: high_cost_payday_cash_advance_deposits?,
        high_cost_payday_cash_advance_payments: high_cost_payday_cash_advance_payments?,
        bank_statement_authenticity_scores: extract_bank_statement_authenticity_scores,
        verification_version_id:,
        credit_model_level:
      )
    end

    def extract_matching_account
      return nil if matching_account_from_book_summary.blank?

      {
        account_number: matching_account_from_book_summary['account_number'],
        account_holder: matching_account_from_book_summary['account_holder'],
        account_type: matching_account_from_book_summary['account_type'],
        deposits_by_month: matching_account_from_book_summary['deposits_by_month']
      }.compact
    end

    def bank_statement_end_date
      return @bank_statement_end_date if defined? @bank_statement_end_date

      @bank_statement_end_date = extract_bank_statement_end_date
    end

    def extract_bank_statement_end_date
      return nil if matching_account_from_book_summary.blank?

      matching_account_from_book_summary['periods'].map do |period|
        period['end_date'].to_s.to_date
      rescue Date::Error => e
        Rails.logger.error('Failed to parse bank statement end date.', error_message: e.message,
                                                                       end_date: period['end_date'])
        nil
      end.compact.max
    end

    def extract_bank_account_balance_current
      return nil if matching_account_from_book_summary.blank? || bank_statement_end_date.blank?

      matching_account_from_book_summary.dig('daily_balances', bank_statement_end_date.iso8601)
    end

    def extract_bank_account_balance_30_days_ago
      return nil if matching_account_from_book_summary.blank? || bank_statement_end_date.blank?

      # Use the oldest ending daily balance between 31 and 25 days prior to the end date of the statement.
      eligible_dates = ((bank_statement_end_date - 31.days)..(bank_statement_end_date - 25.days)).map(&:iso8601)
      matching_account_from_book_summary['daily_balances']&.values_at(*eligible_dates)&.compact&.first
    end

    def count_transactions_past_30_days(enriched_transaction_key)
      return nil if enriched_transactions_report.blank?

      enriched_transactions = enriched_transactions_report.response['enriched_transactions']
      return nil if enriched_transactions.blank?

      matching_account_pk = matching_account_from_book_summary&.dig('bank_account_pk')
      return nil if matching_account_pk.blank?

      enriched_transactions.count do |enriched_transaction|
        countable_past_30_days_transaction?(enriched_transaction, matching_account_pk, enriched_transaction_key)
      end
    end

    def countable_past_30_days_transaction?(enriched_transaction, matching_account_pk, enriched_transaction_key)
      transaction_date = enriched_transaction['txn_date'].to_s.to_date

      enriched_transaction[enriched_transaction_key] == true &&
        enriched_transaction['bank_account_pk'] == matching_account_pk &&
        transaction_date.present? &&
        transaction_date >= bank_statement_end_date - 30.days
    rescue Date::Error => e
      Rails.logger.error('Failed to parse transaction date.', error_message: e.message,
                                                              txn_date: enriched_transaction['txn_date'])
      false
    end

    def count_nsf_count_by_description
      return nil if enriched_transactions_report.blank? || bank_statement_end_date.blank?

      nsf_transactions_by_description.length
    end

    def count_negative_daily_balances_in_past_30_days
      return nil if book_summary.blank? || bank_statement_end_date.blank?

      ((bank_statement_end_date - 30.days)..bank_statement_end_date).count do |date|
        matching_account_from_book_summary.dig('daily_balances', date.iso8601)&.negative?
      end
    end

    def personal_loan_deposits?
      return true if knockout_transactions[:personal_loan_deposits]&.present?

      false
    end

    def high_cost_payday_cash_advance_deposits?
      return true if knockout_transactions[:high_cost_payday_deposits]&.present?

      false
    end

    def high_cost_payday_cash_advance_payments?
      return true if knockout_transactions[:high_cost_payday_payments]&.present?

      false
    end

    def extract_bank_statement_authenticity_scores
      return nil if book_fraud_signals_report.blank?

      (book_fraud_signals_report.response['doc_analysis'] || []).flat_map do |doc_analysis|
        (doc_analysis['form_analysis'] || []).map do |form_analysis|
          form_analysis.dig('form_authenticity', 'score')
        end
      end.compact
    end

    def matching_account_from_book_summary
      return @matching_account_from_book_summary if defined? @matching_account_from_book_summary
      return @matching_account_from_book_summary = nil if book_summary.blank?

      @matching_account_from_book_summary =
        Ocrolus::IdentifyBookSummaryBankAccount.call(book_summary_ocrolus_report: book_summary, bank_account:)
    end

    def knockout_transactions
      return @knockout_transactions if defined? @knockout_transactions
      return @knockout_transactions = {} if enriched_transactions_report.blank?

      @knockout_transactions =
        AutomatedVerification::IdentifyKnockoutTransactions.call(report: enriched_transactions_report,
                                                                 bank_statement_end_date:)
    end

    def nsf_transactions_by_description
      @nsf_transactions_by_description ||=
        if enriched_transactions_report.blank? || bank_statement_end_date.blank?
          []
        else
          AutomatedVerification::IdentifyNsfTransactions.call(report: enriched_transactions_report,
                                                              bank_statement_end_date:)
        end
    end

    def update_todo_doc_statuses
      if verification_inputs.complete?
        status = TodoDoc.statuses[:approved]
      else
        status = TodoDoc.statuses[:rejected]
        rejected_reason = TodoDoc::INCOMPLETE_REJECTED_REASON
      end

      Todos::SetPendingTodoDocStatuses.call(loan:, todo: bank_todo, status:, rejected_reason:)
    end

    def capture_verification_inputs_audit
      notify_audit_event(
        name: 'verification_inputs',
        success: true,
        meta: verification_inputs_audit_meta
      )
    end

    def verification_inputs_audit_meta # rubocop:disable Metrics/AbcSize
      {
        verification_version_id:,
        source: :ocrolus,
        book_summary_report_id: book_summary&.id,
        enriched_transactions_report_id: enriched_transactions_report&.id,
        book_fraud_signals_report_id: book_fraud_signals_report&.id,
        personal_loan_deposit_ids: knockout_transactions[:personal_loan_deposits]&.to_json,
        high_cost_payday_cash_advance_deposit_ids: knockout_transactions[:high_cost_payday_deposits]&.to_json,
        high_cost_payday_cash_advance_payment_ids: knockout_transactions[:high_cost_payday_payments]&.to_json,
        nsf_transactions_by_description: nsf_transactions_by_description.to_json,
        experiment_cohort:,
        credit_model_level:
      }
    end

    def book_summary
      @book_summary ||= ocrolus_reports.detect do |ocrolus_report|
        ocrolus_report.report_type == OcrolusReport::BOOK_SUMMARY_TYPE
      end
    end

    def enriched_transactions_report
      @enriched_transactions_report ||= ocrolus_reports.detect do |ocrolus_report|
        ocrolus_report.report_type == OcrolusReport::ENRICHED_TRANSACTIONS_TYPE
      end
    end

    def book_fraud_signals_report
      @book_fraud_signals_report ||= ocrolus_reports.detect do |ocrolus_report|
        ocrolus_report.report_type == OcrolusReport::BOOK_FRAUD_SIGNALS_TYPE
      end
    end

    def verification_version_id
      @verification_version_id ||= GetVerificationVersion.call
    end

    def experiment_cohort
      @experiment_cohort ||= Experiment['2025_04_CHI_1753_Credit_Model_1_0'].fetch_cohort_for(loan.borrower)
    end

    def credit_model_challenger?
      return false unless Flipper.enabled?(:experiment_2025_04_CHI_1753_Credit_Model_1_0)

      experiment_cohort == 'challenger'
    end

    def credit_model_level
      return 'n/a' unless Flipper.enabled?(:experiment_2025_04_CHI_1753_Credit_Model_1_0)
      return 'n/a' unless credit_model_challenger?

      loan.loan_detail&.credit_model_level || 'unavailable'
    end
  end
end
