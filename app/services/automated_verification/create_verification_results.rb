# frozen_string_literal: true

# rubocop:disable Naming/PredicateMethod
module AutomatedVerification
  class CreateVerificationResults < Service::Base
    MAX_PERMITTED_NEGATIVE_TRANSACTION_COUNT = 3
    MIN_AUTHENTICITY_SCORE = 50

    RULE_NAMES = [
      MATCHING_ACCOUNT_RULE = 'matching_account',
      BANK_STATEMENT_FROM_PAST_35_DAYS_RULE = 'bank_statement_from_past_35_days',
      POSITIVE_CURRENT_BANK_ACCOUNT_BALANCE_RULE = 'positive_current_bank_account_balance',
      POSITIVE_30_DAYS_AGO_BANK_ACCOUNT_BALANCE_RULE = 'positive_30_days_ago_bank_account_balance',
      LIMITED_NEGATIVE_TRANSACTIONS_IN_PAST_30_DAYS_RULE = 'limited_negative_transactions_in_past_30_days',
      NEGATIVE_DAILY_BALANCE_CHECK_RULE = 'negative_daily_balance_check',
      LIMITED_NEGATIVE_DAILY_BALANCES_RULE = 'limited_negative_daily_balances',
      CONSISTENT_NEGATIVE_DAILY_BALANCE_AND_NSF_COUNTS_RULE = 'consistent_negative_daily_balance_and_nsf_counts',
      NO_KNOCKOUT_TRANSACTIONS_RULE = 'no_knockout_transactions',
      AUTHENTIC_BANK_STATEMENT_DOCUMENTS_RULE = 'authentic_bank_statement_documents',
      AUTOPAY_ENABLED_RULE = 'autopay_enabled'
    ].freeze

    BANK_STATEMENT_APPROVAL_RULES = RULE_NAMES

    POSITIVE_BALANCE_RULES = [
      POSITIVE_CURRENT_BANK_ACCOUNT_BALANCE_RULE,
      POSITIVE_30_DAYS_AGO_BANK_ACCOUNT_BALANCE_RULE
    ].freeze

    BANK_STATEMENT_REJECTION_RULES = POSITIVE_BALANCE_RULES + [
      LIMITED_NEGATIVE_TRANSACTIONS_IN_PAST_30_DAYS_RULE,
      LIMITED_NEGATIVE_DAILY_BALANCES_RULE,
      NO_KNOCKOUT_TRANSACTIONS_RULE
    ].freeze

    attribute :verification_inputs, type_for(VerificationInputs)

    delegate :loan, to: :verification_inputs

    def call
      VerificationResults.create!(verification_inputs:, bank_statement_result:, rules_output:)
    end

    private

    def bank_statement_result
      return VerificationResults::APPROVED_RESULT if approve_bank_statement?
      return VerificationResults::REJECTED_RESULT if reject_bank_statement?

      VerificationResults::MANUAL_REVIEW_RESULT
    end

    def bank_statement_approval_rules
      BANK_STATEMENT_APPROVAL_RULES
    end

    def bank_statement_rejection_rules
      BANK_STATEMENT_REJECTION_RULES
    end

    def approve_bank_statement?
      rules_output.slice(*bank_statement_approval_rules).values.all?
    end

    def reject_bank_statement?
      return false unless Flipper.enabled?(:automated_verification_declines)
      return false unless verification_inputs.complete?

      failed_rules = rules_output.slice(*bank_statement_rejection_rules).select { |_, v| v == false }.keys
      return false if failed_rules.empty?

      # For Ocrolus, if the only failed rules are positive balance checks, the bank statement should only
      # be rejected if the negative_daily_balance_check passed (i.e. at least one NSF/overdraft was found)
      if verification_inputs.ocrolus? && only_positive_balance_rules_failed?(failed_rules)
        return rules_output[NEGATIVE_DAILY_BALANCE_CHECK_RULE]
      end

      true
    end

    def only_positive_balance_rules_failed?(failed_rules)
      (failed_rules - POSITIVE_BALANCE_RULES).empty?
    end

    def rules_output
      @rules_output ||= {
        matching_account:,
        bank_statement_from_past_35_days:,
        positive_current_bank_account_balance:,
        positive_30_days_ago_bank_account_balance:,
        limited_negative_transactions_in_past_30_days:,
        negative_daily_balance_check:,
        limited_negative_daily_balances:,
        consistent_negative_daily_balance_and_nsf_counts:,
        no_knockout_transactions:,
        authentic_bank_statement_documents:,
        autopay_enabled:
      }.stringify_keys
    end

    def matching_account
      verification_inputs.matching_account.present?
    end

    def bank_statement_from_past_35_days
      verification_inputs.bank_statement_end_date.present? &&
        verification_inputs.bank_statement_end_date >= Time.zone.today - 35.days
    end

    def positive_current_bank_account_balance
      verification_inputs.bank_account_balance_current.present? &&
        verification_inputs.bank_account_balance_current.positive?
    end

    def positive_30_days_ago_bank_account_balance
      verification_inputs.bank_account_balance_30_days_ago.present? &&
        verification_inputs.bank_account_balance_30_days_ago.positive?
    end

    # Flags loans with more than the permitted number of NSF or overdraft transactions.
    # NOTE: The nsfs_30_days and overdrafts_30_days are intended to be deprecated and replaced by the
    #       nsf_count_by_description. This change requires analysis by and coordination with the Credit Team.
    def limited_negative_transactions_in_past_30_days
      return false if verification_inputs.nsfs_30_days.blank? || verification_inputs.overdrafts_30_days.blank?

      negative_transaction_count = verification_inputs.nsfs_30_days + verification_inputs.overdrafts_30_days
      negative_transaction_count <= MAX_PERMITTED_NEGATIVE_TRANSACTION_COUNT
    end

    # Flags loans whose negative daily balance count conflicts with the reported NSF/overdraft transaction count.
    # A bank statement with negative daily balances but no NSFs/overdraft transactions seems suspect and should be
    # reviewed manually.
    # NOTE: The nsfs_30_days and overdrafts_30_days are intended to be deprecated and replaced by the
    #       nsf_count_by_description. This change requires analysis by and coordination with the Credit Team.
    #       Once that refactoring is completed, this rule can be removed as it would be a duplicate of the
    #       consistent_negative_daily_balance_and_nsf_counts rule. At that time, we'll need to migrate all usages
    #       of this rule, including in the reject_bank_statement? method, over to it's replacement.
    def negative_daily_balance_check
      if verification_inputs.nsfs_30_days.blank? ||
         verification_inputs.overdrafts_30_days.blank? ||
         verification_inputs.negative_daily_balances_30_days.blank?
        return false
      end

      verification_inputs.negative_daily_balances_30_days.zero? ||
        (verification_inputs.nsfs_30_days + verification_inputs.overdrafts_30_days).positive?
    end

    # Flags loans with a high number of negative daily balances. However if there are no NSF transactions reported,
    # we can't trust the negative daily balance count and as a result should have this rule pass to avoid declining
    # these loans.
    def limited_negative_daily_balances
      if verification_inputs.negative_daily_balances_30_days.blank? ||
         verification_inputs.nsf_count_by_description.blank?
        return false
      end

      verification_inputs.negative_daily_balances_30_days < 4 ||
        verification_inputs.nsf_count_by_description.zero?
    end

    # Flags loans with negative daily balance entries but no NSF transactions. In this case, we don't trust the bank
    # statement data and need to manually review the application.
    def consistent_negative_daily_balance_and_nsf_counts
      if verification_inputs.negative_daily_balances_30_days.blank? ||
         verification_inputs.nsf_count_by_description.blank?
        return false
      end

      verification_inputs.negative_daily_balances_30_days.zero? ||
        verification_inputs.nsf_count_by_description.positive?
    end

    # Flags loans with transactions that warrant auto-declining the application (e.g. personal loan deposits, high
    # cost payday loan deposits/payments, etc).
    def no_knockout_transactions
      !verification_inputs.personal_loan_deposits &&
        !verification_inputs.high_cost_payday_cash_advance_deposits &&
        !verification_inputs.high_cost_payday_cash_advance_payments
    end

    def authentic_bank_statement_documents
      return true if verification_inputs.plaid?
      return false if verification_inputs.bank_statement_authenticity_scores.blank?

      verification_inputs.bank_statement_authenticity_scores.all? { |score| score >= MIN_AUTHENTICITY_SCORE }
    end

    def autopay_enabled
      enabled_bank_account&.fund_transfer_authorize == true
    end

    def enabled_bank_account
      BankAccount.enabled_and_belongs_to_an_active_loan(loan.borrower).take
    end
  end
end
# rubocop:enable Naming/PredicateMethod
