# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class Withdraw < ServiceObject
        attribute :request_id, :string

        def call
          call_service_object do
            validate

            LoanApplications::Withdraw.call(loan:, update_case_center: false)
            handle_success
          rescue LoanApplications::Withdraw::WithdrawError => e
            raise MethodNotAllowed, e.message
          end
        end

        private

        def validate
          raise RecordNotFound, "Loan with requestId #{request_id} not found" unless loan
        end

        def loan
          @loan ||= ::Loan.find_by(request_id:)
        end

        def handle_success
          @status = 201
          @body = ::LoanBlueprint.render_as_hash(loan, view: :loan_status_withdrawn)
        end
      end
    end
  end
end
