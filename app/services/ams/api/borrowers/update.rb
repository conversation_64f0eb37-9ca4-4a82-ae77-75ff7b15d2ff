# frozen_string_literal: true

module Ams
  module Api
    module Borrowers
      class Update < ServiceObject
        DISALLOWED_STATUS_ERRORS = {
          LoanAppStatus::APPROVED_STATUS => "You can't edit the loan because it has already been approved.",
          LoanAppStatus::INITIAL_TIL_SUBMIT_STATUS =>
            "You can't edit the loan because TILA has generated,and the customer is ready to sign loan documents.",
          LoanAppStatus::BACK_END_DECLINED_STATUS =>
            "You can't edit the loan because the verifications team declined the Loan application.",
          LoanAppStatus::FRONT_END_DECLINED_STATUS =>
            "You can't edit the loan because the loan application has declined.",
          LoanAppStatus::WITHDRAWN_STATUS =>
            "You can't edit the loan because the agent has withdrawn the loan application.",
          LoanAppStatus::EXPIRED_STATUS => "You can't edit the loan because the loan application has expired.",
          LoanAppStatus::ONBOARDED_STATUS => "You can't edit the loan because the loan has onboarded."
        }.stringify_keys.freeze

        DISALLOWED_STATUSES = DISALLOWED_STATUS_ERRORS.keys.freeze

        ALLOWED_LOAN_STATUSES_FOR_STATE_UPDATE = [
          LoanAppStatus::BASIC_INFO_COMPLETE_STATUS,
          LoanAppStatus::ADD_INFO_COMPLETE_STATUS
        ].freeze

        attribute :unified_id, :string
        attribute :address_apt, :string
        attribute :address_street, :string
        attribute :city, :string
        attribute :current_email, :string
        attribute :date_of_birth, :string # GDS passes date of birth in YYYY-MM-DD format
        attribute :first_name, :string
        attribute :last_name, :string
        attribute :new_email, :string
        attribute :phone_number, :string
        attribute :ssn, :string
        attribute :state, :string
        attribute :zip_code, :string
        attribute :tcpa_accepted, :boolean

        validates :first_name, :last_name, :city, presence: true
        validates_with Validator::Update

        def call
          call_service_object do
            validate_borrower
            validate_loan_status
            validate_state_update

            ApplicationRecord.transaction do
              update_borrower
              update_additional_info
              update_user_email
              handle_success
            rescue ActiveRecord::RecordInvalid => e
              raise UnprocessableEntity, e
            end
          end
        end

        private

        def validate_borrower
          raise RecordNotFound, "borrower with unified_id #{unified_id} not found" if borrower.nil?
        end

        def validate_loan_status
          raise BadRequest, DISALLOWED_STATUS_ERRORS[loan_status] if loan_status.in? DISALLOWED_STATUSES
        end

        def validate_state_update
          return if state.blank? || state == borrower.latest_borrower_info.state

          return if loan_status.in?(ALLOWED_LOAN_STATUSES_FOR_STATE_UPDATE)

          raise BadRequest, "You cannot update the borrower's state because they currently have an active " \
                            'offer associated with a different state. Please withdraw the existing loan and try again.'
        end

        def loan
          @loan ||= ::Loan.find_by(unified_id:)
        end

        def loan_status
          ::LoanAppStatus::ID_TO_NAME[loan.loan_app_status_id]
        end

        def borrower
          @borrower ||= loan&.borrower
        end

        def update_borrower
          sanitized_attributes = borrower_attributes.compact_blank
          # Handle tcpa_accepted_at separately to preserve nil values
          # when tcpa_accepted is false, as compact_blank would remove them
          sanitized_attributes.merge!(tcpa_accepted_at:) unless tcpa_accepted.nil?

          Rails.logger.info('Updating borrower attributes', new_attributes: sanitized_attributes)
          borrower.update!(sanitized_attributes)
        end

        def borrower_attributes
          { email: new_email || current_email,
            first_name:,
            last_name:,
            ssn:,
            date_of_birth: date_of_birth&.to_date }
        end

        def tcpa_accepted_at
          return nil if tcpa_accepted == false
          return borrower.tcpa_accepted_at if borrower.tcpa_accepted_at.present?

          Time.zone.now if tcpa_accepted == true
        end

        def update_additional_info
          return unless borrower_additional_info

          sanitized_attributes = borrower_additional_info_attributes.compact_blank
          Rails.logger.info('Updating borrower additional info attributes',
                            new_attributes: sanitized_attributes,
                            borrower_additional_info_id: borrower_additional_info.id)
          borrower_additional_info.update!(**sanitized_attributes)
        end

        def borrower_additional_info
          @borrower_additional_info ||= borrower.borrower_additional_info.find_by(loan_id: loan.id)
        end

        def borrower_additional_info_attributes
          { address_apt:, address_street:, city:, phone_number:, zip_code:, state: }
        end

        def update_user_email
          return if no_email_update?

          Users::UpdateEmail.call(**user_attributes)
        end

        def no_email_update?
          new_email.blank? || new_email == current_email
        end

        def user_attributes
          { current_email:, updated_email: new_email }
        end

        def handle_success
          @body = BorrowerBlueprint.render_as_hash(borrower.reload, view: :update)
          @status = 200
        end
      end
    end
  end
end
