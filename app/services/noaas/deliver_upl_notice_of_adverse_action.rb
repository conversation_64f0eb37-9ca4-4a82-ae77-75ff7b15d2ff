# frozen_string_literal: true

module Noaas
  class DeliverUplNoticeOfAdverseAction < Service::Base
    attribute :loan_inquiry, type_for(::LoanInquiry)

    delegate :gds_request_id, :loan, to: :loan_inquiry

    def call
      # document from GenerateUplAdverseActionPdf is no longer sent with email
      # but document still needs to be created and stored.
      document = Documents::GenerateUplAdverseActionPdf.call(loan_inquiry:, credit_report_date:)
      Documents::StoreDocument.call(document:, loan:)

      send_email!

      # we need safe op as we send out NOAA without creating a loan record in case of front-end decline
      loan&.update!(adverse_action_sent: Time.current)
    end

    private

    def send_email!
      template_key = Clients::CommunicationsServiceApi::NOTICE_OF_ADVERSE_ACTION_TEMPLATE
      Clients::CommunicationsServiceApi.send_message!(recipient:, template_key:, inputs: template_inputs, attribution:)
    end

    def recipient
      loan_inquiry.application['email']
    end

    def template_inputs # rubocop:disable Metrics/AbcSize
      application = loan_inquiry.application
      decline_details = loan_inquiry.decline

      {
        first_name: application['first_name'].titleize,
        applicant_name: "#{application['first_name']} #{application['last_name']}".titleize,
        applicant_address: "#{application['address_street']} #{application['address_apt']}",
        applicant_city_state_zip: "#{application['city']}, #{application['state_code']} #{application['zip_code']}",
        display_oh_discrimination_disclosure: application['state_code'] == USA_STATES[:OHIO][:abbreviation],
        date: DateHelper.to_ct_date_string(Time.zone.now),
        credit_report_date: DateHelper.to_ct_date_string(credit_report_date),
        gds_decline_reason: decline_details['decline_reason_text'],
        gds_decline_reasons: LoanInquiryDeclineHelper.decline_reasons(loan_inquiry).map { |reason| { reason: } },
        gds_score: decline_details['credit_score']&.truncate || 0,
        factors: LoanInquiryDeclineHelper.score_factors(loan_inquiry),
        is_fcra: LoanInquiryDeclineHelper.noaa_email_fcra?(loan_inquiry),
        is_crb: true,
        is_equifax: true
      }
    end

    def credit_report_date
      return @credit_report_date if defined? @credit_report_date

      borrower_report = Clients::GdsApi::CreditReportParser.from_reports_response(fetch_credit_report)
      @credit_report_date = borrower_report[:credit_report_date]
    rescue StandardError => e
      Rails.logger.warn('Failed to retrieve credit report date', request_id: gds_request_id,
                                                                 error_class: e.class, error_message: e.message)
      @credit_report_date = DateHelper.time_in_ct(Time.zone.now).to_date.iso8601
    end

    def fetch_credit_report
      Clients::GdsApi.retrieve_credit_report(request_id: gds_request_id)
    end

    def attribution
      # we are using the loan_inquiry_id as the APPLICATION attribution so as to not break the existing
      # data team queries. APPLICATION entity is deprecated and should not be used going forward.
      # Deprecation ticket: https://abovelending.atlassian.net/browse/CHI-1070
      Communications::MessageAttribution.call(
        loan:,
        use_loan_inquiry_id_for_application_attribution: true
      )
    end
  end
end
