# frozen_string_literal: true

module Noaas
  class DeliverIplNoticeOfAdverseAction < Service::Base
    attribute :loan, type_for(::Loan)
    attribute :force_delivery, :boolean, default: false

    BACK_END_DECLINED_NOAA_JOB_DELAY = Ams::Api::Loans::FinalDecision::BACK_END_DECLINED_NOAA_JOB_DELAY
    DECLINED_STATUSES = ::LoanAppStatus::DECLINED_STATUSES

    delegate :request_id, to: :loan

    def call
      return if skip_delivery?

      # generated PDF is not sent with email but it still needs to be created and stored for compliance.
      Documents::GenerateAdverseActionPdf.call(loan:, is_fcra:, credit_report_date:)

      send_email!

      loan.update!(adverse_action_sent: Time.current)
    end

    private

    def skip_delivery?
      if !loan_app_status_name.in?(DECLINED_STATUSES)
        Rails.logger.info("Loan #{loan.id} is NOT in declined status, skipping delivery of notice of adverse action",
                          loan_app_status: loan_app_status_name)
        true
      elsif force_delivery
        Rails.logger.info("Loan #{loan.id} has force_delivery flag set, forcing delivery of notice of adverse action")
        false
      elsif loan.back_end_declined? && backend_declined_noaa_sent_recently?
        # Since we delay sending NOAAs for backend-declined loans,
        # we need to check if we've already sent one within the past delay period.
        # There could be two cases:
        # 1. We declined a loan (possibly with the wrong reason) and then re-declined it with the correct reason.
        #    If we did this within the delay period, there would be two NOAA jobs enqueued. We can skip sending the
        #    NOAA during the second job as the first NOAA job already has the updated decline reason.
        # 2. If we re-decline the loan after the delay period (i.e., after the first NOAA was already sent),
        #    we need to let the NOAA job run to send the updated NOAA.

        Rails.logger.info("Loan #{loan.id} has a backend decline adverse action sent recently, " \
                          'skipping delivery of notice of adverse action',
                          adverse_action_sent_at: loan.adverse_action_sent)
        true
      end
    end

    def is_fcra # rubocop:disable Naming/PredicatePrefix
      @is_fcra ||= LoanDeclineHelper.noaa_email_fcra?(loan)
    end

    def loan_app_status_name
      ::LoanAppStatus::ID_TO_NAME[loan.loan_app_status_id]
    end

    def backend_declined_noaa_sent_recently?
      loan.adverse_action_sent.present? && (Time.current - loan.adverse_action_sent) < BACK_END_DECLINED_NOAA_JOB_DELAY
    end

    def send_email!
      template_key = Clients::CommunicationsServiceApi::NOTICE_OF_ADVERSE_ACTION_TEMPLATE
      Clients::CommunicationsServiceApi.send_message!(recipient:, template_key:, inputs: template_inputs, attribution:)
    end

    def recipient
      loan.borrower.email
    end

    def template_inputs # rubocop:disable Metrics/AbcSize
      borrower = loan.borrower
      borrower_additional_info = loan.actual_borrower_additional_info

      {
        first_name: borrower.first_name.titleize,
        applicant_name: "#{borrower.first_name} #{borrower.last_name}".titleize,
        applicant_address: "#{borrower_additional_info.address_street} #{borrower_additional_info.address_apt}",
        applicant_city_state_zip: "#{borrower_additional_info.city}, " \
                                  "#{borrower_additional_info.state} #{borrower_additional_info.zip_code}",
        display_oh_discrimination_disclosure: borrower_additional_info.state == USA_STATES[:OHIO][:abbreviation],
        date: DateHelper.to_ct_date_string(loan.declined_date),
        credit_report_date: credit_report_date,
        gds_decline_reason: loan.decline_reason_text || ::Offer::EXPIRED_TEXT,
        gds_decline_reasons:,
        gds_score: loan.credit_score ? loan.credit_score.truncate : 0,
        factors: parse_loan_factors,
        is_fcra:,
        is_crb: loan.CRB?,
        is_equifax: loan.equifax_product_type?
      }
    end

    def gds_decline_reasons
      reasons = LoanDeclineHelper.decline_reasons(loan) || []
      reasons.map { |reason| { reason: } }
    end

    def parse_loan_factors
      if loan.score_factor.present?
        loan.score_factor.split(';').first(4).map { |factor| { factor: factor.strip } }
      else
        ''
      end
    end

    def credit_report_date
      return @credit_report_date if defined? @credit_report_date

      borrower_report = Clients::GdsApi::CreditReportParser.from_reports_response(fetch_credit_report)
      @credit_report_date = DateHelper.to_ct_date_string(borrower_report[:credit_report_date])
    rescue StandardError => e
      Rails.logger.warn('Failed to retrieve credit report date', loan_id: loan.id,
                                                                 error_message: e.message, error_class: e.class)
      @credit_report_date = DateHelper.to_ct_date_string(loan.created_at)
    end

    def fetch_credit_report
      Clients::GdsApi.retrieve_credit_report(request_id: loan.request_id)
    end

    def attribution
      Communications::MessageAttribution.call(loan:)
    end
  end
end
