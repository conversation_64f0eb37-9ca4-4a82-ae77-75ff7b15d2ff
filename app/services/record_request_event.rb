# frozen_string_literal: true

class RecordRequestEvent < Service::Base
  include Notifier

  attribute :request_event_name, :string
  attribute :request
  attribute :response
  attribute :form_model, type_for(ApplicationFormModel), optional: true
  attribute :meta, type_for(Hash), default: {}

  validates :request_event_name, :request, presence: true
  validates :response, allow_blank: true, presence: true

  alias name request_event_name

  def call
    validate!

    enhance_meta
    create_request_event
  rescue StandardError => e
    Rails.logger.error("RecordRequestEvent error recording #{name} event")
    log_exception(e)
  end

  private

  def create_request_event
    RequestEvent.create(name:, data:, request_id:, metadata:, response: response_attrs)
    notify_request_event(name:, success: success?, meta:, fail_reason:)
  end

  def enhance_meta
    meta.merge!(status_code: response.code)
    meta.merge!(redirected_to: response_redirect_path) if response_redirect?
    meta.merge!(is_form_valid: form_model.errors.blank?) unless form_model.blank? || request.get?
  end

  def success?
    response.successful? || success_redirect?
  end

  # Rack::Response only considers 301-308 to be redirects.
  # See https://github.com/rack/rack/blob/v3.1.10/lib/rack/response.rb#L204
  def response_redirect?
    response.status == 300 || response.redirect?
  end

  def success_redirect?
    response_redirect? && !error_redirect?
  end

  def error_redirect?
    return false unless response_redirect?

    redirect_path = URI.parse(response.redirect_url).path
    whoops_paths = [Rails.application.routes.url_helpers.whoops_exit_pages_path,
                    Rails.application.routes.url_helpers.whoops_servicing_dashboard_index_path]

    whoops_paths.any? { |whoops| redirect_path.include?(whoops) }
  end

  def response_redirect_path
    location = response.location.presence
    return if location.blank?

    uri = URI.parse(location)
    uri.request_uri
  end

  def fail_reason
    return nil if success?
    return 'Error redirect' if error_redirect?
    return response.message if attachment?

    error_from_response
  rescue StandardError
    response.message
  end

  def form_errors
    return nil if form_model.blank? || request.get?

    form_model.errors.messages.transform_values(&:first).values.presence
  end

  def error_from_response
    form_errors&.join(', ') || response_body['error'] || response_body['errors']&.join(', ') || response.message
  end

  def metadata
    @metadata ||= ObservabilityAttributes.attributes.merge(meta).compact
  end

  def request_id
    metadata[:request_id]
  end

  def data
    files = fetch_and_remove_file_params

    filtered_parameters.to_h.merge(files)
  end

  def filtered_parameters
    @filtered_parameters ||= LogstopGuard.sanitize_object!(request.params)
  end

  def fetch_and_remove_file_params
    files = filtered_parameters.select { |_k, v| v.is_a?(ActionDispatch::Http::UploadedFile) }.keys
    return {} if files.empty?

    files.each { |key| filtered_parameters.delete(key) }

    { files: }
  end

  def response_attrs
    { body: response_body, code: response.code }
  end

  def response_body
    @response_body = defined?(response_body)

    return @response_body = response.headers.slice(*%w[Content-Disposition Content-Type]) if attachment?
    return @response_body = LogstopGuard.sanitize_object!(JSON.parse(response.body)) if json?

    @response_body = LogstopGuard.sanitize_object!(response.body)
  rescue JSON::ParserError
    @response_body = LogstopGuard.sanitize_object!(response.body)
  end

  def attachment?
    response.headers['Content-Disposition']&.to_s&.include?('attachment')
  end

  def json?
    response.headers['Content-Type']&.to_s&.include?('json')
  end
end
