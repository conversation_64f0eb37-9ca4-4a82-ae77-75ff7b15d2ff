# frozen_string_literal: true

module EligibilityFileImporter
  class ImportLeads < Service::Base
    include Notifier

    attribute :raw_leads_data
    validates :raw_leads_data, presence: true

    DEFAULT_ELIGIBILITY_LEVEL = 'Z'
    DEFAULT_ENROLLMENT_DATE = '2020-01-01'
    DEFAULT_SSN = '2955'
    DEFAULT_STRING_VALUE = 'stub'
    LEAD_EXPIRATION_HOURS = 26

    def call
      validated_lead_data = process_data
      upserted_lead_ids = upsert_leads(validated_lead_data)

      update_meta(upserted_lead_ids)
    end

    def meta
      @meta ||= {
        total_leads_count: raw_leads_data.count,
        leads_ingested_count: 0,
        leads_not_ingested_count: 0
      }
    end

    private

    def process_data
      raw_leads_data.map do |record|
        lead_data = collect_lead_data(record)

        lead = Lead.build(lead_data)
        if lead.invalid?
          Rails.logger.error('EligibilityFile - Validation failed for lead',
                             message: lead.errors.full_messages.to_sentence, class: self.class.name,
                             code: lead.code, program_id: lead.program_id,
                             phone_number_length: lead.phone_number&.length)
          next
        end

        lead_data
      end
    end

    def collect_lead_data(record)
      {
        code: record['code'],
        first_name: record['first_name'],
        last_name: record['last_name'],
        phone_number: record['phone_number'],
        program_id: record['program_id'],
        service_entity_name: record['service_entity_name'],
        ssn: Rails.env.production? ? record['ssn'] : DEFAULT_SSN,
        type: Lead::TYPES[:IPL],
        account_number: '',
        cft_account_details: '',
        cft_account_holder_name: '',
        months_since_enrollment: 0,
        loan_details: build_loan_details(record),
        payment_details: build_payment_details(record),
        tradeline_details: build_tradeline_details,
        expiration_date: Time.current + LEAD_EXPIRATION_HOURS.hours,
        updated_at: Time.current
      }
    end

    def build_loan_details(record)
      {
        amount_financed: 0.0,
        beyond_enrollment_date: Date.parse(DEFAULT_ENROLLMENT_DATE),
        consecutive_payments_count: 0,
        eligibility_level: DEFAULT_ELIGIBILITY_LEVEL,
        estimated_cft_deposits: 0.0,
        months_since_enrollment: 0,
        nsfs_12_months: 0,
        nsfs_18_months: 0,
        nsfs_24_months: 0,
        nsfs_4_months: 0,
        nsfs_6_months: 0,
        nsfs_9_months: 0,
        nsfs_lifetime: 0,
        payment_adherence_ratio_3_months: 0.0,
        payment_adherence_ratio_4_months: 0.0,
        payment_adherence_ratio_6_months: 0.0,
        program_duration_in_tmonths: record['program_duration_in_tmonths'],
        total_amount_enrolled_debt: 0.0
      }
    end

    def build_payment_details(record)
      {
        beyond_payment_amount: 0.0,
        beyond_payment_dates: nil,
        beyond_payment_frequency: nil,
        estimated_payoff_amount: record['estimated_payoff_amount'],
        monthly_deposit_amount: record['monthly_deposit_amount']
      }
    end

    def build_tradeline_details
      [{
        original_creditor: DEFAULT_STRING_VALUE,
        settled_tradelined_flag: DEFAULT_STRING_VALUE,
        settlement_percent: 0.1,
        tradeline_account_number: DEFAULT_STRING_VALUE,
        tradeline_estimated_settlement_amount: 1.0,
        tradeline_name: DEFAULT_STRING_VALUE
      }]
    end

    def upsert_leads(leads)
      Lead.upsert_all(leads.compact,
                      unique_by: %i[code type],
                      returning: [:id])
    end

    def update_meta(upserted_leads_ids)
      upserted_leads_count = upserted_leads_ids.count
      meta[:leads_ingested_count] = upserted_leads_count
      meta[:leads_not_ingested_count] = raw_leads_data.count - upserted_leads_count
    end
  end
end
