# frozen_string_literal: true

module LoanApplications
  class Withdraw < Service::Base
    class WithdrawError < StandardError; end

    attribute :loan, type_for(::Loan)
    attribute :update_case_center, :boolean, default: true

    ERROR_WITHDRAWABLE_LOAN_TYPE = 'only DM, UPL and IPL loans types can be withdrawn'
    ERROR_ONBOARDED_LOANS = 'ONBOARDED loans can not be withdrawn'

    def call
      validate
      withdraw_loan
    end

    private

    def validate
      raise WithdrawError, ERROR_WITHDRAWABLE_LOAN_TYPE unless loan.withdrawable?
      raise WithdrawError, ERROR_ONBOARDED_LOANS if loan.onboarded?
    end

    def withdraw_loan
      Rails.logger.info("#{self.class.name} - Withdrawing loan #{loan.id}", loan_id: loan.id,
                                                                            unified_id: loan.unified_id,
                                                                            request_id: loan.request_id)
      ApplicationRecord.transaction do
        withdraw_in_ams
        withdraw_in_case_center if update_case_center
      end
    end

    def withdraw_in_ams
      loan.update!(loan_app_status_id: ::LoanAppStatus.id(::LoanAppStatus::WITHDRAWN_STATUS))
    end

    def withdraw_in_case_center
      Clients::GdsApi.sync_status(request_id: loan.request_id,
                                  product_type: loan.product_type,
                                  status: ::LoanAppStatus::WITHDRAWN_STATUS)
    end
  end
end
