# frozen_string_literal: true

module LoanApplications
  class PresentableOffer < Service::Base
    MEANINGFUL_PAYMENT_DIFFERENCE_THRESHOLD = 5

    attribute :loan, type_for(::Loan)

    delegate :offers, to: :loan

    def call
      # Ensure not only that the offer is present, but also that it has an associated AprCalculation
      # record. This avoids duplicate loanpro calls, as the APR calculations are fetched during offer generation.
      # We want to continue waiting until that process is complete.
      return unless primary_offer&.apr_calculations.present?

      mark_as_shown_to_customer(primary_offer)

      primary_offer
    end

    private

    # The primary offer is defined here as the one GDS designated as "Best Matched", if any,
    # falling back on the one with payment amount closest to the deposit amount from the customer's
    # debt resolution program. Sometimes referred to as the "hero" offer, although the offer.is_hero
    # attribute is not used here.
    def primary_offer
      return @primary_offer if defined?(@primary_offer)

      @primary_offer = best_matched_offer || closest_payment_offer
    end

    def best_matched_offer
      offers.detect { |offer| Offer::BEST_MATCHED_OFFER_DESCRIPTION =~ offer.description }
    end

    def closest_payment_offer
      offers.reject { |offer| offer.monthly_payment_amount.blank? }
            .min_by { |offer| (offer.monthly_payment_amount - (loan.monthly_deposit_amount || 0)).abs }
    end

    def mark_as_shown_to_customer(offer)
      offer.update(shown_to_customer: true)
    end
  end
end
