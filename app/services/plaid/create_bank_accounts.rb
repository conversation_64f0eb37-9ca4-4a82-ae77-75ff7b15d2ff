# frozen_string_literal: true

module Plaid
  class CreateBankAccounts < Service::Base
    class Error < StandardError; end

    attribute :borrower, type_for(Borrower)
    attribute :public_token, :string

    delegate :loan, to: :borrower

    validates :borrower, :public_token, presence: true

    def call
      validate!
      bank_accounts = record_bank_accounts
      store_plaid_responses(bank_accounts)

      bank_accounts
    rescue Clients::PlaidApi::InvalidResponse => e
      Rails.logger.error('Invalid Plaid API response payload.', validation_errors: e.message)
      @has_valid_plaid_response = false
      raise Error, 'Invalid Plaid API response payload'
    rescue Clients::PlaidApi::Error => e
      @has_error_during_plaid_call = true
      raise Error, "#{e.response_status} response from Plaid API"
    end

    def meta
      {
        is_valid: errors.blank?,
        errors: errors_for_meta,
        customer_bank_accounts_count: @customer_bank_accounts_count.to_i,
        bank_accounts_upsert_count: @bank_accounts_upsert_count.to_i,
        plaid_reports_created_count: @plaid_reports_created_count.to_i,
        has_valid_plaid_response: valid_plaid_response?,
        has_error_during_plaid_call: @has_error_during_plaid_call || false
      }
    end

    private

    delegate :access_token, to: :plaid_api_token

    def plaid_api_token
      @plaid_api_token ||= Clients::PlaidApi.exchange_public_token(public_token)
    end

    def plaid_auth_results
      @plaid_auth_results ||= Clients::PlaidApi.get_auth_data(plaid_api_token.access_token)
    end

    def plaid_identity_results
      @plaid_identity_results ||= Clients::PlaidApi.get_identity_data(plaid_api_token.access_token)
    end

    def retrieve_institution(institution_id)
      institution_data_cache[institution_id] ||= Clients::PlaidApi.get_institution(institution_id)
    end

    def institution_data_cache
      @institution_data_cache ||= {}
    end

    def record_bank_accounts
      accounts = plaid_auth_results.accounts
      @customer_bank_accounts_count = accounts.count

      account_owners = plaid_identity_results.account_owners

      accounts.map do |plaid_bank_account|
        account_owner = account_owners.find { |entity| entity.plaid_id == plaid_bank_account.plaid_id }
        institution = retrieve_institution(plaid_bank_account.institution_id)

        upsert_bank_account(plaid_api_token, plaid_bank_account, account_owner, institution)
      end
    end

    def upsert_bank_account(plaid_api_token, plaid_bank_account, account_owner, institution)
      attributes = build_bank_account(plaid_api_token, plaid_bank_account, account_owner, institution)

      bank_account = BankAccount.find_or_initialize_by(
        loan:,
        borrower:,
        account_number: attributes[:account_number],
        routing_number: attributes[:routing_number]
      ) do |new_record|
        new_record.id = SecureRandom.uuid
      end

      bank_account.update!(attributes)
      @bank_accounts_upsert_count = (@bank_accounts_upsert_count || 0) + 1
      bank_account
    end

    def build_bank_account(plaid_api_token, plaid_bank_account, account_owner, institution)
      {
        account_number: plaid_bank_account.account_number,
        account_type: plaid_bank_account.type,
        bank: institution.name,
        enabled: false,
        fund_transfer_authorize: false,
        holder_firstname: account_owner.first_name,
        holder_lastname: account_owner.last_name,
        last_four_account_number: plaid_bank_account.last_four_account_number,
        last_four_routing_number: plaid_bank_account.routing_number.last(4),
        plaid_access_token: plaid_api_token.access_token,
        plaid_account_id: plaid_bank_account.plaid_id,
        plaid_item_id: plaid_api_token.item_id,
        routing_number: plaid_bank_account.routing_number
      }
    end

    def store_plaid_responses(bank_accounts)
      bank_accounts.each do |bank_account|
        PlaidReport.create!(parsed_plaid_reports(bank_account))
        Plaid::StoreAdditionalReportsJob.perform_async(bank_account.id)
        @plaid_reports_created_count = (@plaid_reports_created_count || 0) + 1
      end
    rescue StandardError => e
      Rails.logger.error('Error during storing of plaid responses', exception: e)
      ExceptionLogger.error(e)
    end

    def parsed_plaid_reports(bank_account)
      institution_report_responses = institution_data_cache.map { |_key, data| data.response.body }

      [
        { report_type: 'auth', response: plaid_auth_results.response.body, loan:, bank_account: },
        { report_type: 'identity', response: plaid_identity_results.response.body, loan:, bank_account: },
        { report_type: 'institution', response: institution_report_responses, loan:, bank_account: }
      ]
    end

    def valid_plaid_response?
      return @has_valid_plaid_response unless @has_valid_plaid_response.nil?

      # We don't have a valid plaid response if we have an error during plaid call
      @has_error_during_plaid_call ? false : true
    end
  end
end
