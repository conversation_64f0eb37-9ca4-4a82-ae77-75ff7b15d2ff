# frozen_string_literal: true

# Fetches the active LoanPro loan via API and returns the `loanpro_loans` from loanpro_loan_id.
#
# Note: The returned loan may not be in a valid active loanpro_loans record nor does it guarantee it is usable.
# Valid `loanpro_loans` active loans should not be soft-deleted and must have a `til_sign_date` set.
module Servicing
  class LoadActiveLoanproLoanFromApi < Service::Base
    attribute :unified_id

    def call
      fetch_loanpro_loan
    end

    private

    def fetch_loanpro_loan
      response = Clients::LoanproApi.get_active_loanpro_loan_by(unified_id:)
      return unless response.dig('summary', 'total') == 1

      loan_id = response.dig('results', 0, 'id')
      LoanproLoan.where(loanpro_loan_id: loan_id).order(created_at: :desc).first
    end
  end
end
