# frozen_string_literal: true

module Servicing
  class PaymentScheduledEmail < Service::Base
    attribute :loan_pro_loan_id
    attribute :payment_amount
    attribute :payment_date

    CHECKING_ACCOUNT_TYPE = 'paymentAccount.type.checking'

    def call
      Clients::CommunicationsServiceApi.send_message!(
        recipient: email,
        template_key: Clients::CommunicationsServiceApi::CIP_PAYMENT_SCHEDULED,
        inputs: inputs,
        attribution: nil
      )
    rescue StandardError => e
      Rails.logger.error("Failed to send payment scheduled email: #{e.message}", loan_pro_loan_id:)
      raise e
    end

    def inputs
      {
        bank_name:,
        first_name:,
        last_four: bank_account_number.to_s.last(4).to_i,
        message_send_date: CentralTimeZone.now.to_date.iso8601,
        payment_amount: payment_amount&.to_f,
        payment_date: payment_date&.to_date&.iso8601
      }
    end

    def customer_data
      @customer_data ||= Clients::LoanproApi.get_loan(loan_pro_loan_id, expand: 'Customers')
    end

    def loanpro_customer_id
      customer_data&.dig('Customers', 0, 'id')
    end

    def payment_profiles
      @payment_profiles ||= Clients::LoanproApi.fetch_primary_payment_profile(
        loanpro_customer_id, %w[CheckingAccount]
      )['results']
    end

    def primary_checking_account
      @primary_checking_account = payment_profiles.find do |payment_profile|
        payment_profile['isPrimary'] == 1 && payment_profile['type'] == CHECKING_ACCOUNT_TYPE
      end
    end

    def bank_name
      primary_checking_account.dig('CheckingAccount', 'bankName')
    end

    def bank_account_number
      primary_checking_account.dig('CheckingAccount', 'accountNumber')
    end

    def first_name
      customer_data&.dig('Customers', 0, 'firstName')
    end

    def email
      customer_data&.dig('Customers', 0, 'email')
    end
  end
end
