# frozen_string_literal: true

module Upl
  class SendOfferEmail < Service::Base
    attribute :loan_inquiry, type_for(::LoanInquiry)

    def call
      Clients::CommunicationsServiceApi.send_message!(
        recipient: email,
        template_key: Clients::CommunicationsServiceApi::UPL_OFFER_TEMPLATE,
        inputs: {
          first_name:,
          link: build_credit_disclosures_link,
          display_oh_discrimination_disclosure:,
          offers: email_offers
        },
        attribution:
      )
    end

    private

    def email_offers
      @email_offers ||= loan_inquiry.offers.map do |offer|
        {
          lender: 'Above Lending',
          amount: offer['amount'],
          term: offer['term'],
          monthly_payment: offer['payment_amount'],
          interest_rate: (offer['interest_rate'] || 0).round(2),
          apr: offer['apr'],
          origination_fee: offer['origination_fee_amount']
        }
      end
    end

    def build_credit_disclosures_link
      loan_credit_data = ::LoanCreditData.find_or_create_by(credit_report_hash:) do |record|
        record.attributes = {
          id: SecureRandom.uuid,
          loan_inquiry_id: loan_inquiry.id,
          beyond_request_tracking_id: loan_inquiry.beyond_request_tracking_id
        }
        credit_report_details = Clients::GdsApi::CreditReportParser.from_reports_response(fetch_credit_report)
        # Ignore any credit report details that are not recorded in the LoanCreditData model.
        record.attributes = credit_report_details.slice(*::LoanCreditData.attribute_names.map(&:to_sym))
      end

      "#{front_end_base_url}/api/disclosures/credit?q=#{loan_credit_data.credit_report_hash}"
    end

    def credit_report_hash
      return @credit_report_hash if @credit_report_hash.present?

      key = "#{loan_inquiry.gds_request_id}:#{loan_inquiry.id}:#{loan_inquiry.beyond_request_tracking_id}"
      @credit_report_hash = Digest::SHA2.new(512).hexdigest(key)
    end

    def fetch_credit_report
      Clients::GdsApi.retrieve_credit_report(request_id: loan_inquiry.gds_request_id)
    end

    def email
      @email ||= loan_inquiry.application['email']
    end

    def first_name
      @first_name ||= loan_inquiry.application['first_name']
    end

    def display_oh_discrimination_disclosure # rubocop:disable Naming/PredicateMethod
      loan_inquiry.application['state_code'] == USA_STATES[:OHIO][:abbreviation]
    end

    def attribution
      Communications::MessageAttribution.call(loan_inquiry:)
    end

    def front_end_base_url
      Rails.application.config_for(:general).lander_base_url
    end
  end
end
