# frozen_string_literal: true

module LoanApplications
  class SelectOfferFormModel < ApplicationFormModel
    attribute :loan_id
    attribute :offer
    attribute :service_entity

    def offer_id
      offer.id
    end

    def borrower
      loan&.borrower
    end

    private

    def loan
      return @loan if defined?(@loan)

      @loan = ::Loan.includes(:loan_detail).find_by(id: loan_id)
    end
  end
end
