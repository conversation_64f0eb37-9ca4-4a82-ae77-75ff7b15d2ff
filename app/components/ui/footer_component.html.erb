<footer class="bg-brand-gray-200 border-t border-brand-gray-300 px-6 lg:px-4 py-8 <%= additional_classes %>">

  <% if credibility %>

    <div class="flex gap-5 items-center justify-around">
      <%= render UI::TrustPilot::SummaryComponent.new(summary: trustpilot_summary) %>

      <%= link_to 'https://www.bbb.org/us/il/northbrook/profile/loans/above-lending-inc-0654-1000097755/#sealclick',
                  class: 'block', target: '_blank', title: 'View Better Business Bureau profile' do %>
        <%= vite_image_tag 'images/BBB-logo.png', alt: 'Better Business Bureau logo' %>
      <% end %>
    </div>

  <% end %>

  <% if contact_info %>

    <div class="max-w-(--breakpoint-2xl) mx-auto py-6">
      <div class="grid gap-6 md:grid-cols-5 lg:grid-cols-4 text-sm">
        <div class="md:px-6">
          <%= link_to root_path, class: 'block', title: 'Go to Above Lending homepage' do %>
            <%= vite_image_tag 'images/logo-v2.svg', class: 'w-[92px]', alt: 'Above Lending logo' %>
          <% end %>
        </div>
        <div class="md:col-span-2 lg:col-span-1">
          <div class="mb-4">
            <h2 class="mb-2 font-bold">Corporate Address</h2>
            <address class="not-italic">
              <p>650 Dundee Road</p>
              <p>Suite 150</p>
              <p>Northbrook, IL 60062</p>
            </address>
          </div>
          <p class="mb-4">
            <%= link_to '<EMAIL>', 'mailto:<EMAIL>',
                        class: 'underline text-brand-blue-500' %>
          </p>
          <p>
            <%= link_to '(*************', 'tel:**********', class: 'underline text-brand-blue-500',
                                                            data: { testid: 'phone-number' } %>
          </p>
          <p>8 AM - 7 PM CT, Monday - Friday</p>
        </div>
        <div class="md:col-span-2">
          <h2 class="mb-2 font-bold">Mail your payment</h2>
          <p class="mb-4">
            Please send your check payments to the following address<br class="hidden lg:inline">
            and include your account number on the check.
          </p>
          <p>Above Lending</p>
          <address class="not-italic">
            <p>650 Dundee Road</p>
            <p>Suite 150</p>
            <p>Northbrook, IL 60062</p>
          </address>
        </div>
      </div>
    </div>

  <% else %>

    <div class="flex items-center justify-center py-6">
      <%= link_to root_path, class: 'block', title: 'Go to Above Lending homepage' do %>
        <%= vite_image_tag 'images/logo-v2.svg', class: 'w-[164px] mx-auto', alt: 'Above Lending logo' %>
      <% end %>
    </div>

  <% end %>

  <hr class="my-8 border-t border-brand-gray-300">

  <div class="max-w-3xl mx-auto">
    <ul class="flex items-center justify-center gap-4 mb-5">
      <li>
        <% if authenticated %>
          <%= link_to 'Sign Out', signout_borrowers_path,
                      class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline', data: { turbo: false } %>
        <% else %>
          <%= link_to 'Sign In', signin_borrowers_path,
                      class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline' %>
        <% end %>
      </li>

      <li>
        <%= link_to 'Help Center', 'https://abovelending.zendesk.com/hc/en-us',
                    class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline', target: '_blank' %>
      </li>

      <li>
        <%= link_to 'Careers', 'https://jobs.lever.co/abovelending',
                    class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline', target: '_blank' %>
      </li>
    </ul>

    <div>
      <h4 class="text-xs font-semibold mb-5 text-center">Documents &amp; Disclosures</h4>
      <ul class="flex flex-col justify-center items-center md:flex-row gap-3 mb-5">
        <li>
          <%= link_to 'Privacy Policy', privacy_policy_documents_path,
                      class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline' %>
        </li>
        <li>
          <%= link_to 'Terms Of Use', terms_of_use_documents_path,
                      class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline' %>
        </li>
        <li>
          <%= link_to 'Licenses', state_licenses_documents_path,
                      class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline' %>
        </li>
        <li>
          <%= link_to 'Privacy Notices', privacy_notices_documents_path,
                      class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline' %>
        </li>
        <li>
          <%= link_to 'Additional Documents & Disclosures', documents_path,
                      class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline' %>
        </li>
      </ul>
    </div>

    <div class="text-xs text-brand-gray-800 leading-normal">
      <% if credit_disclaimer %>
        <p class="mb-5">* Credit can be improved by making on-time loan payments. Results not guaranteed.</p>
      <% end %>

      <p class="mb-5">
        Above Lending, Inc. ("Above")
        (<%= link_to  'NMLS #1869563',
                      'https://www.nmlsconsumeraccess.org/EntityDetails.aspx/COMPANY/1869563',
                      target: :_blank,
                      class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline' %>)
        markets, brokers, and/or originates unsecured consumer installment loans on behalf of itself and its
        <%= link_to 'Lending Partners',
                    partners_documents_path,
                    class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline' %>.
        Offers presented through this website are estimates based on limited information we have about you. Approval
        and loan terms vary based on credit determination and state law. By applying for a loan, you are consenting
        to us sharing your information with Lending Partners and affiliates. Products and services may not be
        available in all states.
      </p>
      <p class="mb-5">We may report information about your account to credit bureaus. Late payments, missed payments, or other defaults on your account may be reflected in your credit report.</p>
      <p class="mb-5 font-bold uppercase">Important information about procedures for opening a new account:</p>
      <p>To help the government fight the funding of terrorism and money laundering activities, Federal law requires all financial institutions to obtain, verify, and record information that identifies each person who opens an account. What this means for you: when you open an account, we will ask for your name, address, date of birth, and other information that will allow us to identify you. We may also ask to see your driver's license or other identifying documents.</p>
    </div>
  </div>
</footer>
