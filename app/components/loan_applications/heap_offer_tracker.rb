# frozen_string_literal: true

module LoanApplications
  class HeapOfferTracker < ApplicationComponent
    include ActionView::Helpers::NumberHelper

    attr_reader :offer, :code

    def initialize(offer:, code:)
      @offer = offer
      @code = code

      super
    end

    protected

    def heap_offer_data
      return unless offer.present?

      {
        offer.id => {
          apr: offer.apr,
          description: offer.description,
          loanAmount: offer.loan_amount,
          paymentAmount: offer.initial_term_payment,
          termInMonths: offer.term_in_months,
          term: offer.term.to_i,
          termFrequency: offer.term_frequency
        }
      }.as_json
    end
  end
end
