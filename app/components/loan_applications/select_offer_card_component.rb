# frozen_string_literal: true

module LoanApplications
  class SelectOfferCardComponent < ApplicationComponent
    include ActionView::Helpers::NumberHelper

    attr_reader :form_model, :code

    delegate :offer, to: :form_model

    def initialize(form_model:, code:)
      @form_model = form_model
      @code = code

      super
    end

    private

    def apr
      return nil unless offer.apr.present?

      Kernel.format('%.2f%%', offer.apr)
    end

    def term_frequency
      offer.term_frequency&.dasherize
    end

    def term_payment
      number_to_currency(offer.initial_term_payment)
    end

    def term_in_months
      offer.term_in_months
    end

    def total_loan_amount
      number_to_currency(offer.loan_amount.to_f + offer.origination_fee.to_f)
    end
  end
end
