<% content_for :sub_header do %>
  <%= render UI::ProgressComponent.new(current_step: 4, total_steps: 5) %>
<% end %>

<%= render partial: 'channels/loan', locals: { loan: current_loan } %>

<% if @form_model.offer.present? %>
  <%= render partial: 'offer_display' %>
<% else %>
  <%= content_tag :div, nil, data: { controller: 'timeout', 'timeout-seconds-value' => @timeout_seconds_left } %>

  <%= render LoanApplications::SelectOfferLoadingComponent.new %>
<% end %>
