<!DOCTYPE html>
<html lang="en">
  <head>
    <%= render Layout::HeadComponent.new %>
  </head>

  <body class="bg-brand-gray-200 min-h-screen font-montserrat text-brand-gray-800">
    <%= turbo_frame_tag('notification') %>
    <%= render UI::HeaderComponent.new(code: session[:code],
                                       service_entity: session[:service_entity],
                                       show_maintenance: Flipper.enabled?(:show_maintenance_banner),
                                       show_menu: @header_menu,
                                       authenticated: borrower_authenticated?,
                                       loan_onboarded: current_loan&.onboarded?,
                                       loan_payable: session[:servicing_loan_payable]) %>

    <%= yield :sub_header %>

    <main class="<%= yield(:main_class) %>">
      <%= yield %>
    </main>

    <%= render LoanApplications::OffersExpiredModalComponent.new if show_offers_expired_modal? %>

    <%= render UI::FooterComponent.new(credit_disclaimer: @footer_credit_disclaimer, contact_info: @footer_contact_info,
                                       credibility: @footer_credibility, additional_classes: yield(:footer_class),
                                       authenticated: borrower_authenticated?) %>

    <%= render Layout::PostPageLoadScriptsComponent.new(code: session[:code], borrower_id: current_borrower&.id,
                                                        loan_id: current_loan&.id, unified_id: current_loan&.unified_id) %>
  </body>
</html>
