<!DOCTYPE html>
<html lang="en">
  <head>
    <%= render Layout::HeadComponent.new %>
  </head>

  <body class="bg-brand-gray-200 min-h-screen font-montserrat text-brand-gray-800">
    <%= turbo_frame_tag('notification') %>
    <%= render UI::HeaderComponent.new(code: session[:code],
                                       service_entity: session[:service_entity],
                                       show_maintenance: Flipper.enabled?(:show_maintenance_banner),
                                       show_menu: @header_menu,
                                       authenticated: borrower_authenticated?,
                                       loan_onboarded: current_loan&.onboarded?,
                                       loan_payable: session[:servicing_loan_payable]) %>

    <%= yield :sub_header %>

    <main class="bg-white border-t border-brand-gray-100 pb-20">
      <div class="max-w-(--breakpoint-2xl) mx-auto px-4 py-10 md:px-6">
        <%= link_to documents_path, class: 'text-brand-teal-700 flex items-center mb-6' do %>
          <span class="font-semibold text-2xl mr-2">‹</span>
          <span data-testid="document-and-disclosure-btn" class="underline">Documents & Disclosures</span>
        <% end unless current_page?(action: :index) %>

        <div data-testid="document-render">
          <div class="lg:grid gap-60 grid-cols-[1fr_0.45fr]">
            <div class="font-light">
              <%= yield %>
            </div>

            <% unless current_page?(action: :index) %>
              <div data-testid="documents-navigation" class="mt-10 md:mt-0">
                <h2 class="font-semibold text-2xl mb-4">Documents & Disclosures</h2>

                <%= render partial: 'documents/nav' %>
              </div>
            <% end %>
          </div>

        </div>
      </div>
    </main>

    <%= render UI::FooterComponent.new(credit_disclaimer: @footer_credit_disclaimer, contact_info: @footer_contact_info,
                                       credibility: @footer_credibility, authenticated: borrower_authenticated?) %>

    <%= render Layout::PostPageLoadScriptsComponent.new(code: session[:code], borrower_id: current_borrower&.id,
                                                        loan_id: current_loan&.id, unified_id: current_loan&.unified_id) %>
  </body>
</html>
