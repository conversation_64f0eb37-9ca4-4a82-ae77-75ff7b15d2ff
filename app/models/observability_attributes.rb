# frozen_string_literal: true

# A set of attributes describing the context of the current request or job
# for better observability. These attributes are set in SetCurrentRequestDetails
# for requests and Sidekiq::TaggedLogging::Server for jobs.
class ObservabilityAttributes < ActiveSupport::CurrentAttributes
  attribute :cf_ray, :cookie_id, :loan_id, :request_id, :borrower_id,
            :cc_request_id, :code, :product_type, :unified_id, :ip_address

  def reset_attributes!(new_attributes)
    reset
    assign_attributes(new_attributes)
  end

  private

  def assign_attributes(new_attributes)
    new_attributes.each { |key, value| public_send("#{key}=", value) }
  end
end
