# frozen_string_literal: true

module Loans
  class AutoWithdrawCronJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans auto_withdraw]

    def perform
      loans_to_withdraw.each do |loan|
        LoanApplications::Withdraw.call(loan:)
        job_stats[:loans_withdrawn] += 1
      rescue StandardError => e
        job_stats[:errors] += 1
        Rails.logger.error("#{self.class.name} - Error withdrawing loan", loan_id: loan.id, error: e.message)
      end

      notify_stats
    end

    private

    def loans_to_withdraw
      # Withdraw loans that meet all of the following criteria:
      # - Older than 28 days
      # - No associated offers
      # - Not in a credit freeze (those are handled by the expire job)
      # - Status is BASIC_INFO_COMPLETE or ADD_INFO_COMPLETE
      # - Product type is IPL
      @loans_to_withdraw ||= ::Loan
                             .left_joins(:loan_detail, :offers)
                             .where(offers: { id: nil })
                             .where(loan_app_status_id: [
                                      ::LoanAppStatus.id(::LoanAppStatus::BASIC_INFO_COMPLETE_STATUS),
                                      ::LoanAppStatus.id(::LoanAppStatus::ADD_INFO_COMPLETE_STATUS)
                                    ])
                             .where(product_type: Loan::IPL_LOAN_PRODUCT_TYPE)
                             .where(loan_details: { credit_freeze_first_seen_at: nil })
                             .where('loans.created_at < ?', 28.days.ago)
    end

    def job_stats
      @job_stats ||= {
        loans_to_withdraw: loans_to_withdraw.size,
        loans_withdrawn: 0,
        errors: 0
      }
    end

    def notify_stats
      if job_stats[:errors].positive?
        notify_async_event(name: event_name, success: false,
                           fail_reason: 'Errors withdrawing loans. See logs for details.', meta: job_stats)
      else
        notify_async_event(name: event_name, success: true, meta: job_stats)
      end
    end
  end
end
