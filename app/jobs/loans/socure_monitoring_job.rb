# frozen_string_literal: true

module Loans
  class SocureMonitoringJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans socure], retry: 3

    class MissingSocureReferenceId < Ams::ServiceObject::RecordNotFound; end
    class SocureMonitoringError < Ams::ServiceObject::UnprocessableEntity; end
    class MissingLoanproRecord < Ams::ServiceObject::RecordNotFound; end

    attr_reader :socure_reference_id, :loan_id, :operation
    attr_accessor :result

    ENABLE_MONITORING = 'enable'

    def perform(loan_id, operation)
      Rails.logger.info("#{self.class} - socure monitoring start.", loan_id:, operation:)

      @loan_id = loan_id
      @operation = operation

      # To be refactored, fetching reports should happen even before submit_til is called
      fetch_origination_reports

      @socure_reference_id = if Rails.env.production?
                               ArixOnboarding::FundingDocuments::Socure.new(loan_id).reference_id
                             else
                               '496f7b36-1dca-47b8-be42-b01afa460f80' # Socure only supports pre-configured ids
                             end

      validate!
      update_monitoring

      Rails.logger.info("#{self.class} - socure monitoring complete.", meta:)
      notify_async_event(name: event_name, success: true, meta:)
    rescue StandardError => e
      handle_error(exception: e)
      raise e
    end

    private

    def validate!
      return unless @socure_reference_id.blank?

      raise MissingSocureReferenceId, "Socure reference ID not found for loan_id: #{loan_id}"
    end

    def update_monitoring
      @result = Clients::SocureApi.update_transaction_monitoring(@socure_reference_id, operation)

      if @result.success
        update_socure_record
        raise MissingLoanproRecord, "Loanpro record is missing for loan id #{loan_id}" unless loanpro_loan.present?

        update_loanpro_checklist_item
      else
        raise SocureMonitoringError,
              "Socure monitoring for transaction #{@socure_reference_id} failed with an error : #{@result.message}"
      end
    end

    def update_socure_record
      ActiveRecord::Base.transaction do
        socure_monitoring_record = SocureMonitoring.find_or_create_by(loan_id:, socure_reference_id:)

        old_status = socure_monitoring_record.monitoring_enabled
        socure_monitoring_record.update!(monitoring_enabled: operation == ENABLE_MONITORING, source: 'gds')
        SocureTransactionHistory.create!(socure_monitoring_id: socure_monitoring_record.id,
                                         old_status: old_status, new_status: operation == ENABLE_MONITORING)
      end
    end

    def update_loanpro_checklist_item
      item_id = config.checklist_items[:socure_monitoring_is_active]
      value = operation == ENABLE_MONITORING
      Clients::LoanproApi.update_checklist(loanpro_loan_id: loanpro_loan.loanpro_loan_id, item_id:, value:)
    end

    def loanpro_loan
      @loanpro_loan ||= LoanproLoan.where(loan_id:).latest_signed.first
    end

    def config
      @config ||= Rails.application.config_for(:loanpro_api)
    end

    def fetch_origination_reports
      Onboarding::FetchOriginationReport.call(loan_id:)
    end

    def meta
      @meta = { loan_id:, socure_reference_id:, operation: }
    end

    def handle_error(exception:)
      Rails.logger.error("#{self.class} - Socure monitoring failed.", meta:)
      notify_async_event(name: event_name, success: false, fail_reason: exception.message, meta:)
    end
  end
end
