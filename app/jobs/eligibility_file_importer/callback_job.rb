# frozen_string_literal: true

module EligibilityFileImporter
  class CallbackJob
    include Notifier

    def on_success(status, options)
      meta = meta_from_options(options)

      Rails.logger.info('Eligibility file import: completed successfully', status:, meta:)
      notify_async_event(name: 'EligibilityFileImport', success: true, meta:)

      archive_eligibility_file(options)
    end

    def on_death(status, options)
      meta = meta_from_options(options)
      Rails.logger.error('Eligibility file import: batch job died', status:, meta:)

      notify_async_event(
        name: 'EligibilityFileImport',
        success: false,
        fail_reason: 'A job in the batch failed. Check logs for more details.',
        meta:
      )
    end

    private

    def meta_from_options(options)
      target_date = options['target_date']
      leads_ingested_count = Lead.where('updated_at > ?', target_date).count
      start_time = options['start_time']

      {
        target_date:,
        expected_eligible_leads: options['leads_count'],
        leads_ingested_count:,
        leads_not_ingested_count: options['leads_count'] - leads_ingested_count,
        import_duration: Time.current.to_i - start_time
      }
    end

    def archive_eligibility_file(options)
      # TODO: during eligibility file cleanup, refactor Clients::BeyondEligibilityFiles to accept date string
      eligibility_file_date = options['target_date']
      parsed_date = Time.zone.parse(eligibility_file_date).to_date
      Clients::BeyondEligibilityFiles.archive(parsed_date)
    end
  end
end
