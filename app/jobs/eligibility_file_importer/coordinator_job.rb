# frozen_string_literal: true

module EligibilityFileImporter
  class CoordinatorJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[eligibility_file_importer coordinator], retry: 10

    BATCH_SIZE = 500

    BEYOND_TO_AMS_ATTRIBUTE_NAME_MAP = {
      program_name: :program_id,
      service_entity_name: :service_entity_name,
      activation_code: :code,
      client_first_name: :first_name,
      client_last_name: :last_name,
      client_last_four_ssn: :ssn,
      client_phone_number: :phone_number,
      program_duration_in_tmonths: :program_duration_in_tmonths,
      monthly_deposit_amount: :monthly_deposit_amount,
      estimated_payoff_amount: :estimated_payoff_amount
    }.freeze

    AMS_LEAD_ATTRIBUTE_NAMES = BEYOND_TO_AMS_ATTRIBUTE_NAME_MAP.values

    class FileNotFound < StandardError; end

    def perform(target_date = Time.current.to_date)
      @target_date = target_date

      Rails.logger.info("#{self.class}: Starting eligibility file import for target date #{@target_date}")

      eligibility_file = retrieve_eligibility_file

      file_metrics = calculate_file_metrics(eligibility_file.path)
      Rails.logger.info("#{self.class}: File metrics", file_metrics)

      batch_process_eligibility_file(eligibility_file.path, file_metrics[:line_count])
      Rails.logger.info("#{self.class}: Successfully initiated batch processing")
    rescue StandardError => e
      Rails.logger.error("#{self.class}: Failed to process eligibility file", error: e.message,
                                                                              stack_trace: e.backtrace)
      raise
    ensure
      eligibility_file&.close
      eligibility_file&.unlink
    end

    private

    attr_reader :target_date

    def retrieve_eligibility_file
      Rails.logger.info("#{self.class}: Retrieving eligibility file for target date #{target_date}")

      eligibility_file = Clients::BeyondEligibilityFiles.retrieve(target_date)

      if eligibility_file.blank?
        raise FileNotFound, "#{self.class}: Failed to retrieve eligibility file for target date #{target_date}"
      end

      eligibility_file
    end

    def calculate_file_metrics(file_path)
      {
        file_size_kb: (File.size(file_path) / 1024.0).round(2),
        line_count: File.foreach(file_path).count,
        checksum: Digest::MD5.hexdigest(File.read(file_path))
      }
    end

    def batch_process_eligibility_file(file_path, csv_line_count)
      leads_count = csv_line_count - 1 # Subtract 1 from line_count to account for CSV header row
      batch = initialize_sidekiq_batch(leads_count)

      SmarterCSV.process(file_path, csv_parsing_options) do |chunk|
        batch.jobs do
          WorkerJob.perform_async(chunk.map(&:stringify_keys))
        end
      end
    end

    def initialize_sidekiq_batch(leads_count)
      batch = Sidekiq::Batch.new
      batch.description = "Importing eligibility file for target date #{target_date}"

      callback_options = { leads_count:, target_date: target_date.to_s, start_time: Time.current.to_i }
      batch.on(:success, CallbackJob, callback_options)
      batch.on(:death, CallbackJob, callback_options)
      batch
    end

    def csv_parsing_options
      {
        chunk_size: BATCH_SIZE,
        with_line_numbers: true,
        key_mapping: BEYOND_TO_AMS_ATTRIBUTE_NAME_MAP,
        required_keys: AMS_LEAD_ATTRIBUTE_NAMES,
        remove_empty_values: false
      }
    end
  end
end
