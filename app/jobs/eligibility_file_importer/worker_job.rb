# frozen_string_literal: true

module EligibilityFileImporter
  class WorkerJob < ApplicationJob
    sidekiq_options queue: 'critical', tags: %w[eligibility_file_importer worker], retry: 10

    def perform(raw_leads_data)
      @raw_leads_data = raw_leads_data

      process_leads
    rescue StandardError => e
      Rails.logger.error("#{self.class} - Batch processing failed", meta: raw_leads_meta,
                                                                    error: e.message,
                                                                    stack_trace: e.backtrace)
      raise e
    end

    private

    attr_reader :raw_leads_data

    def raw_leads_meta
      @raw_leads_meta ||= {
        csv_start_line: raw_leads_data.first['csv_line_number'],
        csv_end_line: raw_leads_data.last['csv_line_number']
      }
    end

    def process_leads
      Rails.logger.info("#{self.class} - Starting batch processing", meta: raw_leads_meta)

      start_time = Time.current.to_i
      import_leads_service.call
      import_duration = Time.current.to_i - start_time

      Rails.logger.info("#{self.class} - Batch processing completed",
                        meta: raw_leads_meta.merge(import_leads_service.meta).merge(import_duration:))
    end

    def import_leads_service
      @import_leads_service ||= EligibilityFileImporter::ImportLeads.new(raw_leads_data:)
    end
  end
end
