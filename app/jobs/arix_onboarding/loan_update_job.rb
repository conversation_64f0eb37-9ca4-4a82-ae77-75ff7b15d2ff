# frozen_string_literal: true

module ArixOnboarding
  class LoanUpdateJob < ApplicationJob
    class ProcessingError < StandardError; end

    EVENT_NAME = 'arix_onboarding_loan_update_job'

    attr_reader :loan

    sidekiq_options queue: 'default', retry: false

    def perform(unified_id)
      @loan = ::Loan.find_by(unified_id:)

      unless loan.present?
        Rails.logger.error('ArixOnboarding::LoanUpdateJob - Loan not found', class: self.class,
                                                                             unified_id:)
        raise ProcessingError, "Loan not found for unified_id: #{unified_id}"
      end
      loan_update!
    end

    def loan_update!
      Rails.logger.info 'Running ArixOnboarding::LoanUpdateJob', class: self.class, loan_id: loan.id
      validate_funding_status
      update_processing_status!
      store_backup!
      send_arix_loan_update!
      send_event(success: true)
    rescue StandardError => e
      send_event(success: false, fail_reason: "#{e.class}: #{e.message}")

      raise e
    end

    # TODO : Move this logic to model
    def validate_funding_status
      valid_statuses = [::Loan::COMPLIANCE_FAILED, ::Loan::NOT_FULLY_FUNDED,
                        ::Loan::REJECTED, ::Loan::CANCELLED]

      return if valid_statuses.include?(loan.arix_funding_status.funding_status)

      Rails.logger.error('ArixOnboarding::LoanUpdateJob - Cannot update loan', class: self.class, loan_id: loan.id)
      raise ProcessingError,
            "Cannot update loan in status #{loan.arix_funding_status.funding_status} for loan_id: #{loan.id}"
    end

    def update_processing_status!
      loan.update!(arix_onboarding_status: ::Loan::IN_ARIX_LOAN_UPDATE)
    end

    def store_backup!
      Rails.logger.info('ArixOnboarding - Starting to store Arix loan update payload', class: self.class, bucket_name:,
                                                                                       file_key:, loan_id: loan.id)
      s3_client.put_object(bucket: bucket_name, key: file_key, body: arix_loan_update_payload.to_json)

      Rails.logger.info('ArixOnboarding - Arix loan update payload stored', class: self.class, bucket_name:,
                                                                            file_key:, loan_id: loan.id)
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def bucket_name
      @bucket_name ||= Rails.application.config_for(:aws).aws_s3_bucket_name
    end

    def file_key
      return @file_key if defined?(@file_key)

      @file_key = "arix_request_payloads/#{Rails.env}/#{loan.id}/loan_update-#{time_now.to_i}.json"
    end

    def arix_loan_update_payload
      @arix_loan_update_payload ||= if loan.product_type == 'UPL'
                                      UnsecuredPersonalLoan::ArixPayload.call(loan.id)
                                    else
                                      AboveGraduationLoan::ArixPayload.call(loan.id)
                                    end
    end

    def send_arix_loan_update!
      Clients::ArixApi.new.update_loan!(loan.arix_funding_status.arix_loan_id, arix_loan_update_payload)
      Rails.logger.info 'ArixOnboarding::LoanUpdateJob - Sent Arix update loan', class: self.class, loan_id: loan.id
    end

    def meta_attributes
      { loan_id: loan&.id, bucket_name:, file_key: }
    end

    def time_now
      @time_now ||= Time.zone.now
    end

    def send_event(success:, fail_reason: nil)
      notify(EVENT_NAME,
             {
               success:,
               fail_reason:,
               meta: meta_attributes
             })
    end
  end
end
