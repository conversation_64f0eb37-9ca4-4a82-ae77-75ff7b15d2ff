# frozen_string_literal: true

module Servicing
  class SendPaymentScheduledEmailJob < ApplicationJob
    sidekiq_options queue: 'default'

    def perform(loan_pro_loan_id, payment_amount, payment_date)
      Servicing::PaymentScheduledEmail.call(loan_pro_loan_id:, payment_amount:, payment_date:)
      notify_async_event(name: event_name, success: true, meta: { loan_pro_loan_id:, payment_amount:, payment_date: })
    rescue StandardError => e
      Rails.logger.error("Failed to send payment scheduled email: #{e.message}",
                         meta: { loan_pro_loan_id:, payment_amount:, payment_date: })
      notify_async_event(name: event_name, success: false, fail_reason: e.message,
                         meta: { loan_pro_loan_id:, payment_amount:, payment_date: })
      raise e
    end
  end
end
