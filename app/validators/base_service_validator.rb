# frozen_string_literal: true

class BaseServiceValidator < ActiveModel::Validator
  def validate_greater_than_or_equal(record, attribute, path)
    attribute_value = record.send(attribute)

    return unless validate_is_number(record, attribute, path)
    return unless attribute_value.negative?

    message = 'must be larger than or equal to 0'
    params = {
      path:,
      value: attribute_value.to_s.ends_with?('.0') ? attribute_value.to_i : attribute_value,
      type: :number_min,
      context: { limit: 0 }
    }

    add_error(record, attribute, message, params)
  end

  def validate_is_number(record, attribute, path) # rubocop:disable Naming/PredicateMethod
    attribute_value = record.send(attribute)
    return true if [Integer, Float].include?(attribute_value.class)

    message = 'must be a number'
    params = {
      path:,
      value: attribute_value.nil? ? '' : attribute_value,
      type: :number_base
    }

    add_error(record, attribute, message, params)

    false
  end

  def validate_format(record, attribute, path, regex:, sl_pattern:, label: nil, filter_attr_value: false) # rubocop:disable Metrics/ParameterLists
    attribute_value = record.send(attribute)
    regex_test = regex =~ attribute_value
    return unless regex_test.nil?

    attribute_value = 'Filtered123!' if filter_attr_value

    message = "with value \"#{attribute_value}\" fails to match the required pattern: #{sl_pattern}"
    params = {
      path:,
      value: attribute_value,
      label:,
      type: :invalid,
      context: { regex: {} }
    }
    add_error(record, attribute, message, params)
  end

  def validate_string_length(record, attribute, path, maximum_length: nil, label: nil, filter_attr_value: false) # rubocop:disable Metrics/ParameterLists
    attribute_value = record.send(attribute)
    return unless attribute_value && attribute_value.size > maximum_length

    attribute_value = 'Filtered123!' if filter_attr_value

    message = "length must be less than or equal to #{maximum_length} characters long"
    params = {
      path:,
      type: :length,
      value: attribute_value,
      context: { limit: maximum_length },
      label:
    }
    add_error(record, attribute, message, params)
  end

  def validate_empty(record, attribute, path, label: nil)
    return if record.send(attribute).present?

    params = {
      type: :string_empty,
      path:,
      value: '',
      label:
    }

    add_error(record, attribute, 'is not allowed to be empty', params)
  end

  def validate_presence(record, attribute, path)
    return if record.send(attribute)

    add_error(record, attribute, 'is required', { path:, type: :blank })
  end

  def validate_us_state(record, attribute, path)
    attribute_value = record.send(attribute)
    return if attribute_value.blank?

    usa_states = USA_STATES.values.map { |state| state[:abbreviation] }

    return if attribute_value.in?(usa_states)

    add_error(record, attribute, 'is not a valid US state', { path:, type: :invalid })
  end

  def add_error(record, attribute, message, params)
    record.errors.add(
      attribute.to_sym,
      message,
      **params
    )
  end
end
