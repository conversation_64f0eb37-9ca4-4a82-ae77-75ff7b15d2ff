# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ObservabilityAttributes, type: :model do
  let(:cf_ray) { SecureRandom.uuid }
  let(:code) { 'ABCDE' }

  before do
    described_class.cf_ray = cf_ray
    described_class.code = code
  end

  describe '.reset_attributes!' do
    let(:new_cf_ray) { SecureRandom.uuid }
    let(:request_id) { SecureRandom.uuid }

    it 'resets attributes and assigns provided values' do
      described_class.reset_attributes!(cf_ray: new_cf_ray, request_id:)

      expect(described_class.cf_ray).to eq(new_cf_ray)
      expect(described_class.request_id).to eq(request_id)
      expect(described_class.code).to be_nil
    end
  end
end
