# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanApplications::PresentableOffer, type: :service do
  describe '.call' do
    subject(:service) { described_class.new(loan:) }

    let(:loan) { create(:loan, monthly_deposit_amount: 500) }

    context 'when a Best Matched offer is present' do
      let!(:best_matched_offer) { create(:offer, :with_apr_calc, loan:, initial_term_payment: 450, description: 'Best Matched Payment to Debt Resolution Program Deposit') }
      let!(:other_offer) { create(:offer, :with_apr_calc, loan:, initial_term_payment: 500, description: 'Not Best') }

      it 'returns the Best Matched offer' do
        expect(service.call).to eq(best_matched_offer)
        expect(best_matched_offer.reload.shown_to_customer).to be true
        expect(other_offer.reload.shown_to_customer).to be false
      end
    end

    context 'when no APR calculations are present' do
      let!(:best_matched_offer) { create(:offer, loan:, initial_term_payment: 450, description: 'Best Matched Payment to Debt Resolution Program Deposit') }
      let!(:other_offer) { create(:offer, loan:, initial_term_payment: 500, description: 'Not Best') }

      it 'does not show the Best Matched offer' do
        expect(service.call).to be_nil
        expect(best_matched_offer.reload.shown_to_customer).to be false
        expect(other_offer.reload.shown_to_customer).to be false
      end
    end

    context 'when no Best Matched offer is present' do
      let!(:closest_payment_offer) { create(:offer, :with_apr_calc, loan:, initial_term_payment: 510) }
      let!(:other_offer) { create(:offer, :with_apr_calc, loan:, initial_term_payment: 400) }
      let!(:missing_payment_amount_offer) { create(:offer, :with_apr_calc, loan:, initial_term_payment: nil) }

      it 'returns the offer with the payment amount closest to the monthly deposit amount' do
        expect(service.call).to eq(closest_payment_offer)
        expect(closest_payment_offer.reload.shown_to_customer).to be true
        expect(other_offer.reload.shown_to_customer).to be false
        expect(missing_payment_amount_offer.reload.shown_to_customer).to be false
      end
    end

    context 'when there are no offers' do
      it 'returns nil' do
        expect(service.call).to be_nil
      end
    end
  end
end
