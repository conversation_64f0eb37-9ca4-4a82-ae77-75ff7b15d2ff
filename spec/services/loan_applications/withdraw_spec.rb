# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanApplications::Withdraw, type: :service do
  subject(:service) { described_class.new(attributes) }

  let(:loan) { create(:withdrawal_eligible_loan) }
  let(:attributes) { { loan: loan } }
  let!(:withdrawn_status) { create(:loan_app_status, :withdrawn) }

  describe '.call' do
    before do
      allow(Clients::GdsApi).to receive(:sync_status)
    end

    it 'updates the loan status to withdrawn' do
      service.call

      expect(loan.reload.loan_app_status.name).to eq(LoanAppStatus::WITHDRAWN_STATUS)
    end

    it 'syncs the status with case center' do
      service.call

      expect(Clients::GdsApi).to have_received(:sync_status).with(
        request_id: loan.request_id,
        product_type: loan.product_type,
        status: LoanAppStatus::WITHDRAWN_STATUS
      )
    end

    context 'when update_case_center is false' do
      let(:attributes) { { loan: loan, update_case_center: false } }

      it 'does not sync the status with case center' do
        service.call

        expect(Clients::GdsApi).not_to have_received(:sync_status)
      end
    end

    context 'when loan is not withdrawable' do
      let(:loan) { create(:loan, product_type: 'PPC') }

      it 'raises a WithdrawError' do
        expect { service.call }.to raise_error(
          LoanApplications::Withdraw::WithdrawError,
          LoanApplications::Withdraw::ERROR_WITHDRAWABLE_LOAN_TYPE
        )
      end
    end

    context 'when loan is onboarded' do
      before do
        onboarded_status = create(:loan_app_status, name: 'ONBOARDED')
        loan.update!(loan_app_status: onboarded_status)
      end

      it 'raises a WithdrawError' do
        expect { service.call }.to raise_error(
          LoanApplications::Withdraw::WithdrawError,
          LoanApplications::Withdraw::ERROR_ONBOARDED_LOANS
        )
      end
    end

    context 'when there is an error withdrawing the loan from GDS' do
      before do
        allow(Clients::GdsApi).to receive(:sync_status).and_raise(StandardError, 'GDS error')
      end

      it 'raises a WithdrawError' do
        expect { service.call }.to raise_error(
          StandardError,
          'GDS error'
        )
      end
    end
  end
end
