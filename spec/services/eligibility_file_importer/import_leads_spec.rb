# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EligibilityFileImporter::ImportLeads, type: :service do
  include ActiveSupport::Testing::TimeHelpers

  subject(:service) { described_class.new(raw_leads_data:) }
  let(:raw_leads_data) do
    [
      {
        'csv_line_number' => 2,
        'program_id' => 'BRP-12345',
        'code' => 'ABC123',
        'first_name' => '<PERSON>',
        'last_name' => 'Doe',
        'phone_number' => '**********',
        'service_entity_name' => 'Beyond Finance',
        'estimated_payoff_amount' => 12_920.42,
        'monthly_deposit_amount' => 300,
        'program_duration_in_tmonths' => 20,
        'ssn' => '1324'
      },
      {
        'csv_line_number' => 3,
        'program_id' => 'BRP-67890',
        'code' => 'DEF456',
        'first_name' => 'Jane',
        'last_name' => 'Smith',
        'phone_number' => '5558975552',
        'service_entity_name' => 'Beyond Finance',
        'estimated_payoff_amount' => 15_000.00,
        'monthly_deposit_amount' => 350,
        'program_duration_in_tmonths' => 20,
        'ssn' => '1324'
      }
    ]
  end

  before do
    allow(Rails.logger).to receive(:info).and_call_original
    allow(Rails.logger).to receive(:error).and_call_original
  end

  specify 'validations' do
    expect(service).to validate_presence_of(:raw_leads_data)
  end

  describe '.call' do
    subject { service.call }

    it 'processes the raw leads data and creates leads' do
      expect { subject }.to change(Lead, :count).by(2)
    end

    it 'provides meta data' do
      subject

      expected_meta = {
        total_leads_count: 2,
        leads_ingested_count: 2,
        leads_not_ingested_count: 0
      }
      expect(service.meta).to eq(expected_meta)
    end

    it 'sets expiration_date to 26 hrs past the current time' do
      subject

      new_lead = Lead.find_by(code: 'ABC123')
      expiration_date = new_lead[:expiration_date]

      current_time = Time.current
      expected_expiration = current_time + 26.hours
      expect(expiration_date).to be_within(1.minute).of(expected_expiration)
    end

    context 'when a lead has validation errors' do
      let(:raw_leads_data) do
        [
          {
            'csv_line_number' => 2,
            'program_id' => 'BRP-12345',
            'code' => 'K', # too short
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone_number' => '**********',
            'service_entity_name' => 'Beyond Finance'
          }
        ]
      end

      it 'logs the error and does not create the lead' do
        expect { subject }.not_to change(Lead, :count)

        expect(Rails.logger).to have_received(:error).with('EligibilityFile - Validation failed for lead',
                                                           hash_including(
                                                             message: /Code must be at least 6 characters long/,
                                                             class: described_class.name,
                                                             code: 'K',
                                                             program_id: 'BRP-12345',
                                                             phone_number_length: '**********'.length
                                                           ))
      end

      it 'updates the meta data with invalid lead count' do
        subject
        expect(service.meta).to eq({
                                     total_leads_count: 1,
                                     leads_ingested_count: 0,
                                     leads_not_ingested_count: 1
                                   })
      end
    end

    describe 'saved lead attributes' do
      before do
        freeze_time
        allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('production')) # setting to production to test ssn
      end

      it 'sets the correct attributes' do
        subject

        lead = Lead.find_by(code: 'ABC123')
        expect(lead).to have_attributes(
          'code' => 'ABC123',
          'type' => 'IPL',
          'first_name' => 'John',
          'last_name' => 'Doe',
          'phone_number' => '**********',
          'program_id' => 'BRP-12345',
          'service_entity_name' => 'Beyond Finance',
          'ssn' => '1324',
          'expiration_date' => Time.current + 26.hours,
          'months_since_enrollment' => 0,
          'account_number' => '',
          'cft_account_details' => '',
          'cft_account_holder_name' => '',
          'loan_details' => {
            'amount_financed' => 0.0,
            'beyond_enrollment_date' => '2020-01-01',
            'consecutive_payments_count' => 0,
            'eligibility_level' => 'Z',
            'estimated_cft_deposits' => 0.0,
            'months_since_enrollment' => 0,
            'nsfs_12_months' => 0,
            'nsfs_18_months' => 0,
            'nsfs_24_months' => 0,
            'nsfs_4_months' => 0,
            'nsfs_6_months' => 0,
            'nsfs_9_months' => 0,
            'nsfs_lifetime' => 0,
            'payment_adherence_ratio_3_months' => 0.0,
            'payment_adherence_ratio_4_months' => 0.0,
            'payment_adherence_ratio_6_months' => 0.0,
            'program_duration_in_tmonths' => 20,
            'total_amount_enrolled_debt' => 0.0
          },
          'payment_details' => {
            'beyond_payment_amount' => 0.0,
            'beyond_payment_dates' => nil,
            'beyond_payment_frequency' => nil,
            'estimated_payoff_amount' => 12_920.42,
            'monthly_deposit_amount' => 300
          },
          'tradeline_details' => [
            {
              'original_creditor' => 'stub',
              'settled_tradelined_flag' => 'stub',
              'settlement_percent' => 0.1,
              'tradeline_account_number' => 'stub',
              'tradeline_estimated_settlement_amount' => 1.0,
              'tradeline_name' => 'stub'
            }
          ],
          'updated_at' => Time.current
        )
      end

      context 'when rails env is sandbox' do
        before { allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('sandbox')) }

        it 'sets the ssn to 2955' do
          subject
          new_lead = Lead.find_by(code: 'ABC123')
          expect(new_lead.ssn).to eq('2955')
        end
      end
    end
  end
end
