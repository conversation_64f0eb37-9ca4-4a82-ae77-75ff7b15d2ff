# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Servicing::PaymentScheduledEmail do
  let(:loan_pro_loan_id) { '12345' }
  let(:payment_amount) { 1000.0 }
  let(:payment_date) { Date.today }
  let(:email) { '<EMAIL>' }

  let(:payment_profile_response) do
    {
      'results' => [
        {
          'CheckingAccount' => {
            'accountType' => 'bankacct.type.checking',
            'accountNumber' => '************',
            'routingNumber' => '*********',
            'bankName' => 'Wells Fargo',
            'title' => 'Personal Account ********'
          },
          'isPrimary' => 1,
          'type' => 'paymentAccount.type.checking'
        }
      ]
    }
  end

  let(:loan_response) do
    {
      'Customers' => [{ 'id' => 35, 'firstName' => 'test', 'email' => email }]
    }
  end

  let(:inputs) do
    {
      bank_name: 'Wells Fargo',
      first_name: 'test',
      last_four: 3333,
      message_send_date: CentralTimeZone.now.to_date.iso8601,
      payment_amount: payment_amount&.to_f,
      payment_date: payment_date&.iso8601
    }
  end

  let(:template_key) { 'payment_scheduled' }

  before do
    allow(Clients::LoanproApi).to receive(:get_loan).and_return(loan_response)
    allow(Clients::LoanproApi).to receive(:fetch_primary_payment_profile).and_return(payment_profile_response)
    allow(Clients::CommunicationsServiceApi).to receive(:send_message!)
  end

  let(:subject) { Servicing::PaymentScheduledEmail.call(loan_pro_loan_id:, payment_amount:, payment_date:) }

  describe '#call' do
    context 'when communication service send message is called' do
      it 'fetches value from loanpro and builds the input' do
        expect(Clients::CommunicationsServiceApi).to receive(:send_message!).with(recipient: email, template_key:, inputs:, attribution: nil)
        subject
      end
    end

    context 'when loanpro api throws an error' do
      before do
        allow(Clients::LoanproApi).to receive(:get_loan).and_raise(Faraday::Error.new('Connection error'))
      end
      context 'raises an error' do
        it 'raises an error' do
          expect { subject }.to raise_error(Faraday::Error)
        end
      end
    end
  end
end
