# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Servicing::LoadActiveLoanproLoanFromApi do
  let(:loan) { create(:loan) }
  let(:unified_id) { loan.unified_id }
  let(:loanpro_loan) { create(:loanpro_loan, loan:) }
  let(:loanpro_loan_id) { loanpro_loan.loanpro_loan_id }

  let(:loanpro_result) do
    {
      'results' => [
        { 'id' => loanpro_loan_id }
      ],
      'summary' => { 'start' => 0, 'pageSize' => 50, 'total' => 1 }
    }
  end

  before do
    allow(Clients::LoanproApi)
      .to receive(:get_active_loanpro_loan_by)
      .and_return(loanpro_result)
  end

  context 'returns one active loanpro record' do
    it 'returns the loanpro loan' do
      active_loanpro_loan = Servicing::LoadActiveLoanproLoanFromApi.call(unified_id:)

      expect(active_loanpro_loan).to eq(loanpro_loan)
    end

    it 'returns the latest loanpro loan' do
      loanpro_loan_id = loanpro_loan.loanpro_loan_id
      latest_loanpro_loan = create(:loanpro_loan, loan:, loanpro_loan_id:, created_at: Time.now + 1.day)

      active_loanpro_loan = Servicing::LoadActiveLoanproLoanFromApi.call(unified_id:)

      expect(active_loanpro_loan).to eq(latest_loanpro_loan)
    end

    it 'does not care about deleted or not signed records' do
      loanpro_loan.update(deleted_at: Time.now, til_sign_date: nil)

      active_loanpro_loan = Servicing::LoadActiveLoanproLoanFromApi.call(unified_id:)

      expect(active_loanpro_loan).to eq(active_loanpro_loan)
    end
  end

  context 'returns more than on active loanpro record' do
    let(:loanpro_result) do
      {
        'results' => [
          { 'id' => loanpro_loan_id },
          { 'id' => 132_132 }
        ],
        'summary' => { 'start' => 0, 'pageSize' => 50, 'total' => 2 }
      }
    end

    it 'returns nil when more than one loanpro record' do
      active_loanpro_loan = Servicing::LoadActiveLoanproLoanFromApi.call(unified_id:)

      expect(active_loanpro_loan).to be_nil
    end
  end
end
