# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loanpro::CreatePayment do
  include ActiveSupport::Testing::TimeHelpers

  let(:loanpro_loan_id) { '7849' }
  let(:amount) { 100 }
  let(:apply_date) { Date.today.iso8601 }
  let(:payment_profile_id) { 1 }
  let(:apply_timezone) { 'UTC' }
  let(:charge_off_recovery) { false }
  let(:expected_process_date) { apply_date }

  let(:base_payment_attributes) do
    {
      amount: amount.to_d,
      amountType: 'static',
      applyDate: apply_date.to_date.strftime('%F'),
      baProcessor: '1',
      chargeOffRecovery: 0,
      chargeServiceFee: 0,
      daysInPeriod: '',
      lastDayOfMonthEnabled: 0,
      methodType: 'autopay.methodType.echeck',
      name: 'Customer Initiated Payment',
      paymentExtraTowards: 'payment.extra.tx.classicv1',
      paymentMethodAccountType: 'bankacct.type.checking',
      paymentMethodAuthType: 'payment.echeckauth.WEB',
      paymentType: 1,
      payoffAdjustment: 1,
      postPaymentUpdate: 1,
      primaryPaymentMethodId: payment_profile_id,
      processCurrent: true,
      processDateCondition: 'bankingDays',
      processZeroOrNegativeBalance: 0,
      recurringFrequency: 'autopay.recurringFrequency.single',
      recurringPeriods: 1,
      retryDays: 0,
      schedulingType: 'autopay.schedulingType.bankingDayPrior',
      type: 'autopay.type.single'
    }
  end

  let(:payment_attributes) do
    base_payment_attributes.merge(
      processDate: expected_process_date,
      processDateTime: "#{expected_process_date} #{CentralTimeZone.three_pm_utc_hour}:00:00"
    )
  end

  before do
    allow(Clients::LoanproApi).to receive(:create_cip_autopay)
    allow(PaymentHelper).to receive(:time_zone).and_call_original
    allow(PaymentHelper).to receive(:default_autopay_attributes).and_call_original
    allow(CentralTimeZone).to receive(:today).and_call_original
    allow(CentralTimeZone).to receive(:at_2pm).and_call_original
    allow(CentralTimeZone).to receive(:three_pm_utc_hour).and_call_original
    allow(Servicing::SendPaymentScheduledEmailJob).to receive(:perform_async)
  end

  subject do
    described_class.new(
      loanpro_loan_id: loanpro_loan_id,
      amount: amount,
      apply_date: apply_date,
      payment_profile_id: payment_profile_id,
      apply_timezone: apply_timezone,
      charge_off_recovery: charge_off_recovery
    )
  end

  describe '#call' do
    context 'when the customer schedules a payment in HST time zone for today but its after the 2pm central cutoff time' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse("#{Date.today + 1} 3:00:00") }

      let(:apply_date) { Time.find_zone('Hawaii').parse("#{Date.today} 22:00:00").iso8601 }
      let(:expected_process_date) { Date.today.strftime('%Y-%m-%d') }

      it 'schedules the payment on the next day' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    context 'when the customer schedules a payment for today before the 2pm central cutoff time' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse('2023-11-02 9:00:00') }

      let(:apply_date) { Time.find_zone('Central Time (US & Canada)').parse('2023-11-02 9:00:00').iso8601 }

      let(:expected_process_date) { '2023-11-02' }

      it 'schedules the payment for today' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    context 'when the customer schedules a payment for today after the 2pm central cutoff time' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse('2023-11-02 14:30:00') }

      let(:apply_date) { Time.find_zone('Central Time (US & Canada)').parse('2023-11-02 14:30:00').iso8601 }
      let(:expected_process_date) { '2023-11-03' }

      it 'schedules the payment on the next day' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    context 'when the customer schedules a payment in UTC time zone for today after the 2pm central cutoff time' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse("#{Date.today} 18:59:59") }

      let(:apply_date) { Time.find_zone('UTC').parse("#{Date.today} 23:59:59").iso8601 }
      let(:expected_process_date) { (Date.today + 1).strftime('%Y-%m-%d') }

      it 'schedules the payment for today' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    context 'when the customer schedules a payment in UTC time zone nextday after the 2pm central cutoff time' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse("#{Date.today - 1} 19:00:10") }

      let(:apply_date) { Time.find_zone('UTC').parse("#{Date.today + 1} 23:59:59").iso8601 }
      let(:expected_process_date) { (Date.today + 1).strftime('%Y-%m-%d') }

      it 'schedules the payment for today' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    # PST 10am Friday 20th - apply_date
    # Should process on the same day Friday 20th  (before 2pm Central cutoff)
    context 'when the customer schedules a payment in pacfic time zone for today and its before the 2pm central cutoff time' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse("#{Date.today} 12:00:00") }

      let(:apply_date) { Time.find_zone('Pacific Time (US & Canada)').parse("#{Date.today} 10:00:00").iso8601 }
      let(:expected_process_date) { Date.today.strftime('%Y-%m-%d') }

      it 'schedules the payment on the same day' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    # PST 9pm Friday 20th - apply_date
    # Should process on Saturday 21st (after 2pm Central cutoff)
    context 'when the customer schedules a payment in pacfic time zone for today but its after the 2pm central cutoff time' do
      let(:current_time) { Time.find_zone('Central Time (US & Canada)').parse("#{Date.today} 23:00:00") }
      before { travel_to current_time }

      let(:apply_date) { current_time.in_time_zone('Pacific Time (US & Canada)').iso8601 }
      let(:expected_process_date) { (current_time + 1.day).strftime('%Y-%m-%d') }

      it 'schedules the payment on the next day' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    # apply date: 11PM PST 06/27/2025
    # this would be 1am Chicago 06/28/2025
    context 'when the customer schedules a payment in pacfic time zone for today and its next day in CST' do
      let(:current_time) { Time.find_zone('Central Time (US & Canada)').parse("#{Date.today} 1:00:00") }
      before { travel_to current_time }

      let(:apply_date) { current_time.in_time_zone('Pacific Time (US & Canada)').iso8601 }
      let(:expected_process_date) { current_time.strftime('%Y-%m-%d') }

      it 'schedules the payment on the same day' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    context 'when the customer schedules a payment in CET time zone for today before 2pm central time' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse("#{Date.today} 8:00:00") }

      let(:apply_date) { Time.find_zone('Europe/Paris').parse("#{Date.today} 15:00:00").iso8601 }
      let(:expected_process_date) { Date.today.strftime('%Y-%m-%d') }

      it 'schedules the payment on the same day' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    context 'when the customer schedules a payment in CET time zone for today after 2pm central time' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse("#{Date.today} 16:00:00") }

      let(:apply_date) { Time.find_zone('Europe/Paris').parse("#{Date.today} 23:00:00").iso8601 }
      let(:expected_process_date) { (Date.today + 1).strftime('%Y-%m-%d') }

      it 'schedules the payment on the same day' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    context 'when the customer schedules a payment in CET time zone nextday after 2pm central time' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse("#{Date.today - 1} 19:00:00") }

      let(:apply_date) { Time.find_zone('Europe/Paris').parse("#{Date.today + 1} 2:00:00").iso8601 }
      let(:expected_process_date) { (Date.today + 1).strftime('%Y-%m-%d') }

      it 'schedules the payment on the same day' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    context 'when the apply date for a payment is in the past' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse('2025-06-20 23:00:00') }

      let(:apply_date) { Time.find_zone('Pacific Time (US & Canada)').parse('2023-01-11 21:00:00').iso8601 }

      it 'logs the error and raises a CreateAutopayValidationError' do
        expect do
          subject.call
        end.to raise_error(Loanpro::CreatePayment::CreatePaymentSystemError)
        subject.errors.add(:apply_date, :invalid)
      end
    end

    context 'when the customer schedules a payment' do
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse('2023-11-02 9:00:00') }

      let(:apply_date) { Time.find_zone('Central Time (US & Canada)').parse('2023-11-02 9:00:00').iso8601 }

      let(:expected_process_date) { '2023-11-02' }

      it 'successfully schedules a payment email job' do
        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
        expect(Servicing::SendPaymentScheduledEmailJob).to have_received(:perform_async)
      end
    end

    context 'when charge_off_recovery is true' do
      let(:charge_off_recovery) { true }
      before { travel_to Time.find_zone('Central Time (US & Canada)').parse('2023-11-02 9:00:00') }

      let(:apply_date) { '2023-11-02' }
      let(:expected_process_date) { '2023-11-02' }
      let(:apply_date) { Time.find_zone('Central Time (US & Canada)').parse('2023-11-02 9:00:00').iso8601 }

      it 'included charge_off_recovery payment attributes' do
        payment_attributes[:chargeOffRecovery] = 1
        payment_attributes[:payoffAdjustment] = 2
        payment_attributes[:processZeroOrNegativeBalance] = true

        subject.call
        expect(Clients::LoanproApi).to have_received(:create_cip_autopay).with(loanpro_loan_id, payment_attributes)
      end
    end

    context 'when time zone is invalid' do
      before do
        allow(PaymentHelper).to receive(:time_zone).and_return(nil)
      end

      it 'logs an error' do
        subject.call
        Rails.logger.error("No valid timezone found for #{apply_timezone}", loanpro_loan_id:)
      end
    end

    context 'when time zone raises an error' do
      before do
        allow(PaymentHelper).to receive(:time_zone).and_raise(TZInfo::InvalidTimezoneIdentifier)
      end

      it 'raises an error' do
        expect do
          subject.call
        end.to raise_error(Loanpro::CreatePayment::CreatePaymentSystemError)
      end
    end

    context 'when loanpro api call throws an error' do
      it 'logs the error and raises a CreatePaymentSystemError' do
        allow(Clients::LoanproApi).to receive(:create_cip_autopay).and_raise(StandardError, 'Loanpro API error')
        expect do
          subject.call
        end.to raise_error(Loanpro::CreatePayment::CreatePaymentSystemError)
      end
    end
  end
end
