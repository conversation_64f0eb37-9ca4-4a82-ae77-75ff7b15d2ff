# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loanpro::CancelPayment do
  let!(:loan) { create(:loan) }
  let!(:borrower) { create(:borrower, loan:) }
  let(:autopay_id) { '7849' }
  let(:loanpro_loan_id) { '266_986' }
  let(:subject) { described_class.new(borrower:, autopay_id:) }

  let(:autopay_response) do
    {
      'id' => 307_644,
      'loanId' => loanpro_loan_id,
      'name' => 'Scheduled Payment',
      'type' => 'autopay.type.single',
      'status' => 'autopay.status.pending',
      'created' => '/Date(1748382946)/'
    }
  end

  let(:loan_response) do
    {
      'id' => loanpro_loan_id,
      'displayId' => loan.unified_id
    }
  end

  before do
    allow(Clients::LoanproApi).to receive(:get_autopay).and_return(autopay_response)
    allow(Clients::LoanproApi).to receive(:get_loan).and_return(loan_response)
    allow(Clients::LoanproApi).to receive(:cancel_autopay)
  end

  describe '#call' do
    context 'when a loan has single payment type to be cancelled' do
      it 'calls the LoanPro API to fetch autopay and loan' do
        subject.call

        expect(Clients::LoanproApi).to have_received(:get_autopay)
        expect(Clients::LoanproApi).to have_received(:get_loan)
      end

      it 'calls loanpro service to cancel autopay' do
        subject.call

        expect(Clients::LoanproApi).to have_received(:cancel_autopay)
      end
    end

    context 'when autopay entity is missing' do
      before do
        allow(Clients::LoanproApi).to receive(:get_autopay).and_return(nil)
      end
      it 'throws an error' do
        expect { subject.call }.to raise_error(Loanpro::CancelPayment::CancelPaymentSystemError, "cancel payment error - Autopay record not found for ID: #{autopay_id}")
      end
    end

    context 'when autopay type is not single' do
      let(:autopay_response) do
        {
          'id' => 307_644,
          'loanId' => loanpro_loan_id,
          'type' => 'autopay.type.recurring'
        }
      end
      it 'throws an error' do
        expect { subject.call }.to raise_error(
          Loanpro::CancelPayment::CancelPaymentSystemError,
          "cancel payment error - Autopay ID #{autopay_id} cannot be cancelled. Only 'autopay.type.single' type autopays are cancellable."
        )
      end
    end

    context 'when borrower and loanpro loan is different' do
      let(:loan_response) do
        {
          'id' => loanpro_loan_id,
          'displayId' => 1
        }
      end

      it 'throws an error' do
        expect { subject.call }.to raise_error(Loanpro::CancelPayment::CancelPaymentSystemError, "cancel payment error - Loan not found for borrower #{borrower.id}")
      end
    end
  end
end
