# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loanpro::DashboardDetails do
  let(:borrower) { create(:borrower_additional_info, :with_city_and_zip).borrower }
  let(:loan) { create(:loan, borrower:) }
  let(:loan_id) { loan.id }
  let(:dashboard_details_service) { described_class.new(borrower:, loan_id:) }

  before { allow(Clients::LoanproApi).to receive(:fetch_loan_details).with(loan_id, anything).and_return(loan_details) }
  before { allow(Clients::DashServicingApi).to receive(:beneficiary_owner_details).and_return(nil) }

  describe '.call' do
    let(:loan_details) do
      {
        'LoanSetup' => {
          'loanId' => loan_id,
          'apr' => '25.24',
          'loanAmount' => '21585.82',
          'underwriting' => '1196.56',
          'loanTerm' => '54.0000',
          'contractDate' => '/Date(1625702400)/',
          'paymentFrequency' => 'loan.frequency.monthly',
          'tilPaymentSchedule' => '[{"count":54,"payment":710.20000000000005,"startDate":"07/06/2023"}]'
        },
        'StatusArchive' => [{
          'nextPaymentDate' => '/Date(1701820800)/',
          'amountDue' => '0.00',
          'daysPastDue' => 0,
          'loanSubStatusId' => 9,
          'loanStatusText' => 'Open',
          'periodsRemaining' => 47,
          'nextPaymentAmount' => '669.24',
          'netChargeOff' => '0.00',
          'payoff' => '19707.92'
        }],
        'Transactions' => transactions,
        'SubPortfolios' => sub_portfolios_response
      }
    end

    let(:sub_portfolios_response) do
      [{ 'title' => 'CA' },
       { 'title' => 'Debt Validation' }]
    end

    let(:transactions) do
      [{
        'type' => 'reversal',
        'paymentAmount' => '12',
        'date' => '/Date(1704121200)/',
        'paymentPrincipal' => '10',
        'paymentInterest' => '20'
      },
       { # We select the most recent payment
         'type' => 'payment',
         'paymentAmount' => '120',
         'date' => '/Date(1704207600)/',
         'paymentPrincipal' => '11',
         'paymentInterest' => '21'
       },
       {
         'type' => 'payment',
         'paymentAmount' => '100',
         'date' => '/Date(1704294000)/',
         'paymentPrincipal' => '10',
         'paymentInterest' => '20'
       }]
    end

    subject(:response) { dashboard_details_service.call }

    it 'expects to format the loan details correctly' do
      expect(response).to be_a(Clients::DashServicingApi::DashboardDetails)
      expect(response.apr).to eq('25.24')
      expect(response.loan_amount).to eq(21_585.82)
      expect(response.underwriting).to eq(1_196.56)
      expect(response.loan_payment).to eq(710.2)
      expect(response.number_of_terms).to eq(54)
      expect(response.contract_date).to eq(Date.new(2021, 7, 8))
      expect(response.current_due_date).to eq(Date.new(2023, 12, 6))
      expect(response.current_payment_due).to eq(0.0)
      expect(response.days_past_due).to eq(0)
      expect(response.loan_status_text).to eq('Open')
      expect(response.number_of_remaining_terms).to eq(47)
      expect(response.next_payment_amount).to eq(669.24)
      expect(response.next_payment_date).to eq(Date.new(2023, 12, 6))
      expect(response.overdue_amount).to eq(0.0)
      expect(response.payment_frequency).to eq('loan.frequency.monthly')
      expect(response.payoff_amount).to eq(19_707.92)
      expect(response.sub_status).to eq('Good Standing')
      expect(response.sub_status_id).to eq(9)
      expect(response.last_payment.payment_amount).to eq(100.0)
      expect(response.last_payment.date).to eq(Time.utc(2024, 1, 3))
      expect(response.last_payment.payment_principal).to eq(10.0)
      expect(response.last_payment.payment_interest).to eq(20.0)
      expect(response.borrower_name).to eq(borrower.full_name)
      expect(response.address).to eq(borrower.latest_borrower_info.address)
      expect(response.city).to eq(borrower.latest_borrower_info.city)
      expect(response.state).to eq(borrower.latest_borrower_info.state)
      expect(response.zip_code).to eq(borrower.latest_borrower_info.zip_code)
      expect(response.debt_sale).to eq(false)
      expect(response.beneficial_owner_name).to be_nil
      expect(response.beneficial_owner_details).to be_blank
    end

    context 'when LoanPro API call fails' do
      before { allow(Clients::LoanproApi).to receive(:fetch_loan_details).and_raise(StandardError, 'A') }

      it 'raises an error' do
        expect { response }.to raise_error(described_class::DashboardDetailsSystemError, 'dashboard details error - A')
      end
    end
  end

  describe '#remaining_balance' do
    let(:loan_details) do
      {
        'StatusArchive' => [{
          'loanSubStatusText' => sub_status,
          'netChargeOff' => '0.00',
          'payoff' => '19707.92'
        }]
      }
    end

    subject { dashboard_details_service.send(:remaining_balance) }

    context 'when it is charged off' do
      let(:sub_status) { 'Closed - Charged Off' }

      it 'returns net charge off amount' do
        expect(subject).to eq('0.00')
      end
    end

    context 'when it is not charged off' do
      let(:sub_status) { 'Good Standing' }

      it 'returns the payoff amount' do
        expect(subject).to eq('19707.92')
      end
    end
  end

  describe '#last_payment' do
    let(:loan_details) { { 'Transactions' => transactions } }
    let(:transactions) { [] }

    subject { dashboard_details_service.send(:last_payment) }

    context 'when there are no transactions' do
      it 'returns an empty hash' do
        expect(subject).to eq({})
      end
    end

    context 'when there are no payment transactions' do
      let(:transactions) { [{ 'type' => 'refund' }] }

      it 'returns an empty hash' do
        expect(subject).to eq({})
      end
    end

    context 'when there are multiple payments' do
      let(:transactions) do
        [{
          'type' => 'reversal',
          'paymentAmount' => '12',
          'date' => '/Date(1704121200)/',
          'paymentPrincipal' => '10',
          'paymentInterest' => '20'
        },
         {
           'type' => 'payment',
           'paymentAmount' => '120',
           'date' => '/Date(1704207600)/',
           'paymentPrincipal' => '11',
           'paymentInterest' => '21'
         }]
      end

      it 'returns the most recent payment details' do
        expect(subject).to eq({
                                'payment_amount' => '120',
                                'date' => '2024-01-02',
                                'payment_principal' => '11',
                                'payment_interest' => '21'
                              })
      end
    end
  end

  describe '#beneficial_owner_name' do
    let(:loan_details) { { 'SubPortfolios' => sub_portfolios_response } }
    let(:sub_portfolios_response) { [] }

    subject { dashboard_details_service.send(:beneficial_owner_name) }

    context 'when there are no sub-portfolios' do
      it 'returns nil' do
        expect(subject).to be_nil
      end
    end

    context 'when there are no matching sub-portfolios' do
      let(:sub_portfolios_response) { [{ 'title' => 'Other Portfolio' }] }

      it 'returns nil' do
        expect(subject).to be_nil
      end
    end

    context 'when there is a matching sub-portfolio' do
      let(:sub_portfolios_response) { [{ 'title' => 'Titan Asset Purchasing' }] }

      it 'returns the matching beneficial owner name' do
        expect(subject).to eq('Titan Asset Purchasing')
      end
    end
  end

  describe '#beneficial_owner_details' do
    let(:loan_details) { { 'StatusArchive' => [{ 'loanSubStatusId' => 9 }] } }
    subject { dashboard_details_service.send(:beneficial_owner_details) }
    context 'when loan is in open repaying state' do
      it 'returns nil' do
        expect(subject).to be_nil
        expect(Clients::DashServicingApi).not_to have_received(:beneficiary_owner_details)
      end
    end

    context 'when loan is sold' do
      let(:loan_details) { { 'StatusArchive' => [{ 'loanSubStatusId' => 60 }] } }
      it 'calls dash api beneficiary_owner_details' do
        subject
        expect(Clients::DashServicingApi).to have_received(:beneficiary_owner_details)
      end
    end
  end
end
