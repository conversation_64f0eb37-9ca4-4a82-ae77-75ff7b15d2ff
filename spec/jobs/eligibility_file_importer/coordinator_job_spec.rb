# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EligibilityFileImporter::Coordinator<PERSON>ob do
  subject(:job) { described_class }

  let(:target_date) { Time.current.to_date }
  let(:eligibility_mock_file_path) { 'spec/support/files/ipl_identification_mock.csv' }
  let(:eligibility_file) do
    Tempfile.new.tap do |temp|
      temp.write(File.read(eligibility_mock_file_path))
      temp.close
    end
  end

  before do
    allow(Rails.logger).to receive(:info)
    allow(Rails.logger).to receive(:error)
    allow(Clients::BeyondEligibilityFiles).to receive(:retrieve).and_return(eligibility_file)
    allow(EligibilityFileImporter::WorkerJob).to receive(:perform_async)
    set_notifier_stubs
  end

  after { eligibility_file.unlink }

  describe '#perform' do
    it 'calculates and logs file metrics' do
      job.perform_inline(target_date)

      expected_metrics = {
        file_size_kb: File.size(eligibility_mock_file_path) / 1024.0,
        line_count: File.foreach(eligibility_mock_file_path).count,
        checksum: Digest::MD5.hexdigest(File.read(eligibility_mock_file_path))
      }
      expect(Rails.logger).to have_received(:info).with("#{described_class}: File metrics", expected_metrics)
    end

    describe 'testing batch processing' do
      before do
        Sidekiq::Testing.inline! # need inline mode for testing batch processing
        allow(EligibilityFileImporter::WorkerJob).to receive(:perform_async).and_call_original
        stub_const('EligibilityFileImporter::CoordinatorJob::BATCH_SIZE', '4') # Reduce batch size for testing
        allow(Clients::BeyondEligibilityFiles).to receive(:archive) # CallbackJob#on_success is called after batch processing is successful
      end

      after { Sidekiq::Testing.fake! } # revert to fake mode

      let(:expected_worker_args_from_mock) do
        raw_data = SmarterCSV.process(eligibility_mock_file_path)
        raw_data.map.with_index do |row, index|
          {
            'csv_line_number' => index + 2, # account for header
            'program_id' => row[:program_name],
            'code' => row[:activation_code],
            'first_name' => row[:client_first_name] || '',
            'last_name' => row[:client_last_name] || '',
            'phone_number' => row[:client_phone_number],
            'service_entity_name' => row[:service_entity_name],
            'estimated_payoff_amount' => row[:estimated_payoff_amount],
            'monthly_deposit_amount' => row[:monthly_deposit_amount],
            'program_duration_in_tmonths' => row[:program_duration_in_tmonths],
            'ssn' => row[:client_last_four_ssn]
          }
        end
      end

      it 'queues WorkerJob inside the batch' do
        job.perform_inline(target_date)

        # Eligibility mock file contains 10 leads + header
        # We are expecting batch of 3 WorkerJob with 4,4,2 leads respectively
        worker_index = 1
        expect(EligibilityFileImporter::WorkerJob).to have_received(:perform_async).thrice do |chunk|
          case worker_index
          when 1
            expect(chunk.size).to eq(4)
            expect(chunk).to eq(expected_worker_args_from_mock[0..3]) # worker 1 will process rows 1 to 4
          when 2
            expect(chunk.size).to eq(4)
            expect(chunk).to eq(expected_worker_args_from_mock[4..7]) # worker 2 will process rows 5 to 8
          when 3
            expect(chunk.size).to eq(2)
            expect(chunk).to eq(expected_worker_args_from_mock[8..9]) # worker 3 will process rows 9 and 10
          end

          worker_index += 1
        end
      end

      it 'import correct number of leads' do
        # mock file has 10 leads including 4 invalid leads
        expect(SmarterCSV.process(eligibility_mock_file_path).count).to eq(10)

        expect { job.perform_inline(target_date) }.to change(Lead, :count).by(6)
        expect(Rails.logger).to have_received(:error).with('EligibilityFile - Validation failed for lead',
                                                           hash_including(message: 'Code must be at least 6 characters long'))
        expect(Rails.logger).to have_received(:error).with('EligibilityFile - Validation failed for lead',
                                                           hash_including(message: 'First name cannot be empty'))
        expect(Rails.logger).to have_received(:error).with('EligibilityFile - Validation failed for lead',
                                                           hash_including(message: 'Last name cannot be empty'))
        expect(Rails.logger).to have_received(:error).with('EligibilityFile - Validation failed for lead',
                                                           hash_including(message: 'Phone number must be at least 10 digits long'))
      end
      it 'import leads data correctly' do
        job.perform_inline(target_date)

        leads = Lead.all
        expect(leads.count).to eq(6) # there are 6 valid leads and 4 invalid leads from mock file
        expect(leads.map(&:code)).to match_array(%w[LLYuxz 3YihTa Ly1dbD VcEjbx GIe8jT LJe9lp])

        # verifying data for one lead (check row 1 in mock file: `spec/support/files/ipl_identification_mock.csv`)
        lead = leads.find_by(code: 'LLYuxz')
        expect(lead).to have_attributes(
          'code' => 'LLYuxz',
          'type' => 'IPL',
          'first_name' => 'Erica',
          'last_name' => 'Lambert[LLYuxz]',
          'phone_number' => '5558975551',
          'program_id' => 'BRP-126582',
          'service_entity_name' => 'Beyond Finance',
          'ssn' => '2955'
        )
      end

      it 'enqueues the correct callback job after batch processing is complete' do
        job.perform_inline(target_date)

        expected_event = {
          name: 'EligibilityFileImport',
          success: true,
          meta: hash_including({
                                 target_date: target_date.to_date.to_s,
                                 expected_eligible_leads: 10
                               })
        }

        expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_event)
        expect(Clients::BeyondEligibilityFiles).to have_received(:archive).with(target_date)
      end
    end

    context 'when the eligibility file is not found' do
      before do
        allow(Clients::BeyondEligibilityFiles).to receive(:retrieve).and_return(nil)
      end

      it 'raises a FileNotFound error with the appropriate message' do
        expect { job.perform_inline(target_date) }.to raise_error(
          EligibilityFileImporter::CoordinatorJob::FileNotFound,
          "#{described_class}: Failed to retrieve eligibility file for target date #{target_date}"
        )
      end
    end

    context 'when an error occurs during processing' do
      before do
        allow(Clients::BeyondEligibilityFiles).to receive(:retrieve).and_raise(StandardError.new('Test error'))
      end

      it 'logs the error and re-raises it' do
        expect { job.perform_inline(target_date) }.to raise_error(StandardError, 'Test error')

        expect(Rails.logger).to have_received(:error).with(
          "#{described_class}: Failed to process eligibility file",
          hash_including(error: 'Test error', stack_trace: instance_of(Array))
        )
      end
    end
  end
end
