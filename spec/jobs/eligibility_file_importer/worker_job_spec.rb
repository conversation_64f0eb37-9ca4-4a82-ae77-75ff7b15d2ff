# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EligibilityFileImporter::WorkerJob do
  subject(:job) { described_class }
  let(:raw_leads_data) do
    [
      { 'csv_line_number' => 2, 'program_id' => 'BRP-12345', 'code' => 'ABC123' },
      { 'csv_line_number' => 3, 'program_id' => 'BRP-67890', 'code' => 'DEF456' }
    ]
  end
  let(:import_leads_service) { instance_double(EligibilityFileImporter::ImportLeads, call: true, meta: import_leads_meta) }
  let(:import_leads_meta) {  { total_leads_count: 2, leads_ingested_count: 2, leads_not_ingested_count: 0 } }

  before do
    allow(Rails.logger).to receive(:info)
    allow(Rails.logger).to receive(:error)
    allow(EligibilityFileImporter::ImportLeads).to receive(:new).and_return(import_leads_service)
  end

  describe '#perform' do
    it 'processes the raw leads data' do
      job.perform_inline(raw_leads_data)
      expect(EligibilityFileImporter::ImportLeads).to have_received(:new).with(raw_leads_data: raw_leads_data)
      expect(import_leads_service).to have_received(:call)
    end

    it 'logs the start of batch processing' do
      job.perform_inline(raw_leads_data)
      expect(Rails.logger).to have_received(:info).with(
        "#{described_class} - Starting batch processing",
        meta: { csv_start_line: 2, csv_end_line: 3 }
      )
    end

    it 'logs the completion of batch processing' do
      job.perform_inline(raw_leads_data)
      expect(Rails.logger).to have_received(:info).with(
        "#{described_class} - Batch processing completed",
        meta: { csv_start_line: 2, csv_end_line: 3, import_duration: be_a(Integer) }.merge!(import_leads_meta)
      )
    end

    context 'when an error occurs' do
      before do
        allow(import_leads_service).to receive(:call).and_raise(StandardError, 'Test error')
      end

      it 'logs the error' do
        expect { job.perform_inline(raw_leads_data) }.to raise_error(StandardError)
        expect(Rails.logger).to have_received(:error)
          .with("#{described_class} - Batch processing failed", meta: { csv_start_line: 2, csv_end_line: 3 },
                                                                error: 'Test error', stack_trace: instance_of(Array))
      end

      it 'reraises the error' do
        expect { job.perform_inline(raw_leads_data) }.to raise_error(StandardError, 'Test error')
      end
    end
  end
end
