# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EligibilityFileImporter::CallbackJob do
  subject(:job) { described_class.new }

  let(:status) { instance_double(Sidekiq::Batch::Status) }
  let(:target_date) { Time.now.to_date.to_s }
  let(:leads_count) { 100 }
  let(:options) { { 'target_date' => target_date, 'leads_count' => leads_count, 'start_time' => Time.now.to_i } }
  let(:leads_ingested_count) { 90 }

  before do
    allow(Rails.logger).to receive(:info)
    allow(Rails.logger).to receive(:error)
    allow(Clients::BeyondEligibilityFiles).to receive(:archive)
    allow(Lead).to receive(:where).and_return(double(count: leads_ingested_count))
    set_notifier_stubs
  end

  describe '#on_success' do
    it 'logs a success message' do
      job.on_success(status, options)

      expect(Rails.logger).to have_received(:info).with(
        'Eligibility file import: completed successfully',
        hash_including(status: status, meta: hash_including(target_date: target_date))
      )
    end

    it 'notifies of a successful async event' do
      job.on_success(status, options)

      expected_event = {
        name: 'EligibilityFileImport',
        success: true,
        meta: {
          target_date: target_date,
          expected_eligible_leads: leads_count,
          leads_ingested_count: leads_ingested_count,
          leads_not_ingested_count: leads_count - leads_ingested_count,
          import_duration: be_a(Integer)
        }
      }

      expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_event)
    end

    it 'archives the eligibility file' do
      parsed_date = Time.zone.parse(target_date).to_date
      job.on_success(status, options)

      expect(Clients::BeyondEligibilityFiles).to have_received(:archive).with(parsed_date)
    end
  end

  describe '#on_death' do
    it 'logs a death message' do
      job.on_death(status, options)

      expect(Rails.logger).to have_received(:error).with(
        'Eligibility file import: batch job died',
        hash_including(status: status, meta: hash_including(target_date: target_date))
      )
    end

    it 'notifies of a failed async event' do
      job.on_death(status, options)

      expected_event = {
        name: 'EligibilityFileImport',
        success: false,
        fail_reason: 'A job in the batch failed. Check logs for more details.',
        meta: {
          target_date: target_date,
          expected_eligible_leads: leads_count,
          leads_ingested_count: leads_ingested_count,
          leads_not_ingested_count: leads_count - leads_ingested_count,
          import_duration: be_a(Integer)
        }
      }

      expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_event)
    end
  end
end
