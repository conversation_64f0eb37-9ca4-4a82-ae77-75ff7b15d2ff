# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Servicing::SendPaymentScheduledEmailJob do
  subject(:job) { described_class.new }

  let(:loan_pro_loan_id) { '12345' }
  let(:payment_amount) { 1000.0 }
  let(:payment_date) { Date.today }
  let(:meta) { { loan_pro_loan_id:, payment_amount:, payment_date: } }

  before do
    allow(Servicing::PaymentScheduledEmail).to receive(:call).with(loan_pro_loan_id:, payment_amount:, payment_date:)
    set_notifier_stubs
  end

  context 'when job is called' do
    it 'should call PaymentScheduledEmail' do
      expect(Servicing::PaymentScheduledEmail).to receive(:call).with(loan_pro_loan_id:, payment_amount:, payment_date:)
      job.perform(loan_pro_loan_id, payment_amount, payment_date)
    end
  end

  context 'when PaymentScheduledEmail throws an error' do
    let(:error) { StandardError.new('failed') }

    before do
      allow(Servicing::PaymentScheduledEmail).to receive(:call).and_raise(error)
      allow(Rails.logger).to receive(:error)
    end
    it 'should call PaymentScheduledEmail and log the error' do
      expect { job.perform(loan_pro_loan_id, payment_amount, payment_date) }.to raise_error(StandardError)
      expect(Rails.logger).to have_received(:error).with("Failed to send payment scheduled email: #{error.message}", meta:)
      expect_to_notify('AsyncEvent', name: 'SendPaymentScheduledEmailJob', success: false, fail_reason: error.message, meta:)
    end
  end
end
