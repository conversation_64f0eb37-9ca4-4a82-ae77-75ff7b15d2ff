# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loans::AutoWithdrawCronJob, type: :job do
  subject(:job) { described_class.perform_inline }

  let!(:basic_info_status) { create(:loan_app_status, name: LoanAppStatus::BASIC_INFO_COMPLETE_STATUS) }
  let!(:add_info_status) { create(:loan_app_status, name: LoanAppStatus::ADD_INFO_COMPLETE_STATUS) }

  # Eligible loan with BASIC_INFO_COMPLETE status
  let!(:eligible_loan_basic_info) do
    loan = create(:loan, loan_app_status: basic_info_status, product_type: Loan::IPL_LOAN_PRODUCT_TYPE, created_at: 30.days.ago)
    create(:loan_detail, loan: loan, credit_freeze_first_seen_at: nil)
    loan
  end

  # Eligible loan with ADD_INFO_COMPLETE status
  let!(:eligible_loan_add_info) do
    loan = create(:loan, loan_app_status: add_info_status, product_type: Loan::IPL_LOAN_PRODUCT_TYPE, created_at: 30.days.ago)
    create(:loan_detail, loan: loan, credit_freeze_first_seen_at: nil)
    loan
  end

  # Ineligible loan - has offers
  let!(:ineligible_loan_has_offers) do
    loan = create(:loan, loan_app_status: basic_info_status, product_type: Loan::IPL_LOAN_PRODUCT_TYPE, created_at: 30.days.ago)
    create(:loan_detail, loan: loan, credit_freeze_first_seen_at: nil)
    create(:offer, loan: loan)
    loan
  end

  # Ineligible loan - created recently (less than 28 days ago)
  let!(:ineligible_loan_created_recently) do
    loan = create(:loan, loan_app_status: basic_info_status, product_type: Loan::IPL_LOAN_PRODUCT_TYPE, created_at: 20.days.ago)
    create(:loan_detail, loan: loan, credit_freeze_first_seen_at: nil)
    loan
  end

  # Ineligible loan - wrong status
  let!(:ineligible_loan_wrong_status) do
    loan = create(:loan, loan_app_status: create(:loan_app_status, name: LoanAppStatus::OFFERED_STATUS),
                         product_type: Loan::IPL_LOAN_PRODUCT_TYPE, created_at: 30.days.ago)
    create(:loan_detail, loan: loan, credit_freeze_first_seen_at: nil)
    loan
  end

  # Ineligible loan - has credit freeze
  let!(:ineligible_loan_credit_freeze) do
    loan = create(:loan, loan_app_status: basic_info_status, product_type: Loan::IPL_LOAN_PRODUCT_TYPE, created_at: 30.days.ago)
    create(:loan_detail, loan: loan, credit_freeze_first_seen_at: 1.day.ago)
    loan
  end

  # Ineligible loan - wrong product type (UPL instead of IPL)
  let!(:ineligible_loan_wrong_product_type) do
    loan = create(:loan, loan_app_status: basic_info_status, product_type: Loan::UPL_LOAN_PRODUCT_TYPE, created_at: 30.days.ago)
    create(:loan_detail, loan: loan, credit_freeze_first_seen_at: nil)
    loan
  end

  before do
    allow(LoanApplications::Withdraw).to receive(:call)
    set_notifier_stubs
  end

  describe '#perform' do
    it 'withdraws eligible loans' do
      subject

      # Should withdraw both eligible loans
      expect(LoanApplications::Withdraw).to have_received(:call).with(loan: eligible_loan_basic_info).once
      expect(LoanApplications::Withdraw).to have_received(:call).with(loan: eligible_loan_add_info).once

      # Should not withdraw ineligible loans
      expect(LoanApplications::Withdraw).not_to have_received(:call).with(loan: ineligible_loan_has_offers)
      expect(LoanApplications::Withdraw).not_to have_received(:call).with(loan: ineligible_loan_created_recently)
      expect(LoanApplications::Withdraw).not_to have_received(:call).with(loan: ineligible_loan_wrong_status)
      expect(LoanApplications::Withdraw).not_to have_received(:call).with(loan: ineligible_loan_credit_freeze)
      expect(LoanApplications::Withdraw).not_to have_received(:call).with(loan: ineligible_loan_wrong_product_type)
    end

    it 'notifies of successful job completion' do
      subject

      expected_event = {
        name: 'AutoWithdrawCronJob',
        success: true,
        meta: {
          loans_to_withdraw: 2,
          loans_withdrawn: 2,
          errors: 0
        }
      }
      expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_event)
    end

    context 'when an error occurs during withdrawal' do
      before do
        allow(LoanApplications::Withdraw).to receive(:call).with(loan: eligible_loan_basic_info)
                                                           .and_raise(StandardError.new('Test error'))
        allow(Rails.logger).to receive(:error)
      end

      it 'logs the error and continues processing' do
        subject

        expect(Rails.logger).to have_received(:error).with(
          'Loans::AutoWithdrawCronJob - Error withdrawing loan',
          loan_id: eligible_loan_basic_info.id,
          error: 'Test error'
        )
      end

      it 'notifies of job completion with errors' do
        subject

        expected_event = {
          name: 'AutoWithdrawCronJob',
          success: false,
          fail_reason: 'Errors withdrawing loans. See logs for details.',
          meta: {
            loans_to_withdraw: 2,
            loans_withdrawn: 1,
            errors: 1
          }
        }
        expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_event)
      end
    end

    context 'when no eligible loans are found' do
      before do
        eligible_loan_basic_info.loan_detail.destroy
        eligible_loan_basic_info.destroy
        eligible_loan_add_info.loan_detail.destroy
        eligible_loan_add_info.destroy
      end

      it 'notifies of successful job completion with zero loans' do
        subject

        expected_event = {
          name: 'AutoWithdrawCronJob',
          success: true,
          meta: {
            loans_to_withdraw: 0,
            loans_withdrawn: 0,
            errors: 0
          }
        }
        expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_event)
      end
    end
  end
end
