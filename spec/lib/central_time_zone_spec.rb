# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CentralTimeZone do
  include ActiveSupport::Testing::TimeHelpers

  describe '.to_central_time' do
    it 'converts a time to central during daylight savings time' do
      travel_to('2022-03-14T11:00:00Z'.to_time)

      converted_time = CentralTimeZone.to_central_time(Time.now)
      expect(converted_time.zone).to eq('CDT')
    end

    it 'converts a time to central outside of daylight savings time' do
      travel_to('2022-11-07T11:00:00Z'.to_time)

      converted_time = CentralTimeZone.to_central_time(Time.now)
      expect(converted_time.zone).to eq('CST')
    end
  end

  describe '.now' do
    it 'returns the current time in the central time zone during daylight savings time' do
      travel_to('2022-03-14T11:00:00Z'.to_time)

      now = CentralTimeZone.now

      expect(now.hour).to eq(6)
      expect(now.zone).to eq('CDT')
    end

    it 'returns the current time in the central time zone outside of daylight savings time' do
      travel_to('2022-11-07T11:00:00Z'.to_time)

      now = CentralTimeZone.now

      expect(now.hour).to eq(5)
      expect(now.zone).to eq('CST')
    end
  end

  describe '.at_noon' do
    it 'returns noon for the central time zone during daylight savings time' do
      travel_to('2022-03-14'.to_date)

      noon = CentralTimeZone.at_noon

      expect(noon.hour).to eq(12)
      expect(noon.zone).to eq('CDT')
    end

    it 'returns noon for the central time zone outside of daylight savings time' do
      travel_to('2022-11-07'.to_date)

      noon = CentralTimeZone.at_noon

      expect(noon.hour).to eq(12)
      expect(noon.zone).to eq('CST')
    end
  end

  describe '.three_pm_utc_hour' do
    it 'returns the correct 3pm central time hour in UTC during daylight savings time' do
      travel_to('2022-03-14'.to_date)

      expect(CentralTimeZone.three_pm_utc_hour).to eq(20)
    end

    it 'returns the correct 3pm central time hour in UTC outside of daylight savings time' do
      travel_to('2022-11-07'.to_date)

      expect(CentralTimeZone.three_pm_utc_hour).to eq(21)
    end
  end

  describe '.today' do
    it 'returns the date portion of CentralTimeZone.now' do
      travel_to('2024-03-11 14:30:00'.in_time_zone('America/Chicago'))

      expect(CentralTimeZone.today).to eq('2024-03-11'.to_date)
    end
  end
end
