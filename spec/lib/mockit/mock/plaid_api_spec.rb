# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Mockit::Mock::PlaidApi do
  let(:service_key) { 'clients/plaid_api' }

  subject(:plaid_api) do
    Class.new(Clients::PlaidApi) do
      class << self
        %i[create_asset_report create_link_token exchange_public_token get_auth_data get_asset_report get_asset_report_pdf get_identity_data get_institution].each do |meth|
          define_method(meth) do |*_args, **_kwargs|
            meth
          end
        end
      end
    end
  end

  before do
    allow(subject).to receive(:name).and_return('Clients::PlaidApi')
    Mockit.mock_classes(subject => Mockit::Mock::PlaidApi)
  end

  after do
    RequestStore.clear!
  end

  describe 'mocked method' do
    %i[create_asset_report create_link_token exchange_public_token get_auth_data get_asset_report get_asset_report_pdf get_identity_data get_institution].each do |meth|
      it "should call original #{meth} if no mock is set" do
        expect(subject.send(meth)).to eq(meth)
      end
    end
  end

  context 'with mock set' do
    let(:default_overrides) do
      {
        get_asset_report: { a: 'b' }.to_json,
        auth_data: {
          accounts: [
            { account_number: '12345' },
            { name: 'TestAccountName' }
          ]
        },
        identity_data: {
          account_owners: [
            { first_name: 'First' },
            { last_name: 'Last' }
          ]
        }
      }
    end

    before do
      Mockit::Store.mock_id = 1
      Mockit::Store.write(service: service_key, overrides: default_overrides)
    end

    it '#create_asset_report' do
      result = plaid_api.create_asset_report
      expect(result).to be_instance_of(Clients::PlaidApi::AssetReportCreationResults)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
      expect(result.asset_report_id).to eq(1)
      expect(result.asset_report_token).to eq(1)
    end

    context '#get_asset_report' do
      it 'has specific get_asset_report overrides' do
        result = plaid_api.get_asset_report(1)
        expect(result.response).to eq(Mockit::Mock::GenericResponse.new(JSON.parse(default_overrides[:get_asset_report])))
        expect(result.asset_report_id).to be_kind_of(String)
      end

      it 'without specific override falls back to original ' do
        over = default_overrides.delete(:get_asset_report)
        Mockit::Store.write(service: service_key, overrides: over)
        result = plaid_api.get_asset_report(1)
        expect(result).to eq(:get_asset_report)
      end
    end

    it '#create_link_token' do
      result = plaid_api.create_link_token
      expect(result).to be_instance_of(Clients::PlaidApi::LinkToken)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
      expect(result.token).to eq('1')
    end

    it '#exchange_public_token' do
      result = plaid_api.exchange_public_token
      expect(result).to be_instance_of(Clients::PlaidApi::ApiToken)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
      expect(result.access_token).to eq('1')
      expect(result.item_id).to be_kind_of(String)
    end

    it '#get_institution' do
      result = plaid_api.get_institution
      expect(result).to be_instance_of(Clients::PlaidApi::Institution)
      expect(result.response).to be_a_kind_of(Mockit::Mock::GenericResponse)
      expect(result.name).to eq('Wells Fargo')
      expect(result.id).to be_kind_of(String)
    end

    context '#get_auth_data' do
      it 'with specific accounts overrides' do
        result = plaid_api.get_auth_data
        expect(result).to be_instance_of(Clients::PlaidApi::AuthResults)
        result.accounts.each do |acc|
          expect(acc).to be_kind_of(Clients::PlaidApi::BankAccount)
        end
        expect(result.accounts[0].account_number).to eq('12345')
        expect(result.accounts[0].name).to eq('First Mock Checking account')
        expect(result.accounts[1].account_number).to eq('*********')
        expect(result.accounts[1].name).to eq('TestAccountName')
      end

      it 'with empty accounts overrides' do
        default_overrides[:auth_data][:accounts] = []
        Mockit::Store.write(service: service_key, overrides: default_overrides)
        result = plaid_api.get_auth_data
        expect(result).to be_instance_of(Clients::PlaidApi::AuthResults)
        expect(result.accounts).to be_empty
      end

      it 'default mock overrides' do
        default_overrides.delete(:auth_data)
        Mockit::Store.write(service: service_key, overrides: default_overrides)
        result = plaid_api.get_auth_data
        expect(result).to be_instance_of(Clients::PlaidApi::AuthResults)
        expect(result.accounts.size).to eq(1)
        expect(result.accounts.first).to be_kind_of(Clients::PlaidApi::BankAccount)
        expect(result.accounts[0].account_number).to eq('*********')
        expect(result.accounts[0].name).to eq('First Mock Checking account')
      end
    end

    context '#get_identity_data' do
      it 'with specific accounts overrides' do
        result = plaid_api.get_identity_data
        expect(result).to be_instance_of(Clients::PlaidApi::IdentityResults)
        result.account_owners.each do |acc|
          expect(acc).to be_kind_of(Clients::PlaidApi::AccountOwner)
        end
        expect(result.account_owners[0].first_name).to eq('First')
        expect(result.account_owners[0].last_name).to eq('Mockovich')
        expect(result.account_owners[1].first_name).to eq('Mock')
        expect(result.account_owners[1].last_name).to eq('Last')
      end

      it 'with empty accounts overrides' do
        default_overrides[:identity_data][:account_owners] = []
        Mockit::Store.write(service: service_key, overrides: default_overrides)
        result = plaid_api.get_identity_data
        expect(result).to be_instance_of(Clients::PlaidApi::IdentityResults)
        expect(result.account_owners).to be_empty
      end

      it 'default mock overrides' do
        default_overrides.delete(:identity_data)
        Mockit::Store.write(service: service_key, overrides: default_overrides)
        result = plaid_api.get_identity_data
        expect(result).to be_instance_of(Clients::PlaidApi::IdentityResults)
        expect(result.account_owners.size).to eq(1)
        expect(result.account_owners.first).to be_kind_of(Clients::PlaidApi::AccountOwner)
        expect(result.account_owners[0].first_name).to eq('Mock')
        expect(result.account_owners[0].last_name).to eq('Mockovich')
      end
    end
  end
end
