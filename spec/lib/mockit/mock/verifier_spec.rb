# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Mockit::Mock::Verifier do
  describe '.mock_call' do
    subject(:verifier) do
      Class.new(Plaid::Webhooks::Verifier) do
        def call
          'called'
        end
      end
    end

    let(:service_key) { 'plaid/webhooks/verifier' }
    before do
      allow(subject).to receive(:name).and_return('Plaid::Webhooks::Verifier')
      Mockit.mock_classes(subject => Mockit::Mock::Verifier)
    end

    after do
      RequestStore.clear!
    end

    it 'returns true for mocked response' do
      Mockit::Store.mock_id = 1
      Mockit::Store.write(service: service_key, overrides: {})

      expect(subject.call).to eq(true)
    end

    it 'calls original without mock set' do
      expect(subject.call).to eq('called')
    end
  end
end
