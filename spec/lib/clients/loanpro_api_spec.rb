# frozen_string_literal: true

require 'rails_helper'

describe Clients::LoanproApi do
  let(:loanpro_id) { 'sample_id' }
  let(:loanpro_setup_id) { 'sample_setup_id' }
  let(:config) do
    {
      api_token: 'sample_token',
      base_url: 'http://samplebaseurl.com',
      instance_id: 'sample_instance_id',
      custom_fields: { checking_account_entity_ids: 120 }
    }
  end
  let(:response_body) { { 'd' => 'sample data' }.to_json }
  let(:response_headers) { { 'Content-Type' => 'application/json' } }
  let(:offer) { double(:offer, amount_financed: 1000, interest_rate: 5.5, origination_fee: 100, term: 12) }
  let(:contract_date) { '2023-08-04' }
  let(:first_payment_date) { '2023-09-04' }
  let(:payment_frequency) { 'monthly' }
  let(:unified_id) { '12345' }
  let(:temporary_loan_body) do
    {
      temporaryAccount: 1,
      LoanSetup: {
        loanAmount: '1000.00',
        loanRate: '5.500',
        loanRateType: 'loan.rateType.annually',
        underwriting: '100.00',
        loanTerm: offer.term,
        contractDate: contract_date,
        firstPaymentDate: first_payment_date,
        calcType: 'loan.calcType.simpleInterest',
        loanClass: 'loan.class.consumer',
        loanType: 'loan.type.installment',
        paymentFrequency: payment_frequency,
        discount: 0,
        discountSplit: 1,
        discountCalc: 'loan.discountCalc.straightLine'
      },
      loanIdOption: 'loan.idOption.custom',
      displayId: unified_id,
      # Required for LoanPro duplication error
      __ignoreWarnings: true
    }
  end

  before do
    allow(Rails.application).to receive(:config_for).with(:loanpro_api).and_return(config)
  end

  describe '.create_temporary_loan' do
    let(:temporary_loan_body) do
      body = super()
      described_class.send(:apply_ignore_warnings, body)
    end

    it 'sends the correct request to the API' do
      stub_request(:post, "#{config[:base_url]}/odata.svc/Loans")
        .with(body: temporary_loan_body.to_json)
        .to_return(body: response_body, headers: response_headers)

      result = described_class.create_temporary_loan(offer, contract_date, first_payment_date, payment_frequency, unified_id)

      expect(result).to eq('sample data')
      expect_api_event_record(name: 'loanpro_create_temporary_loan')
    end

    it 'sends the correct request to the API in production' do
      allow(Rails.env).to receive(:production?).and_return(true)
      stub_request(:post, "#{config[:base_url]}/odata.svc/Loans")
        .with(body: temporary_loan_body.to_json)
        .to_return(body: response_body, headers: response_headers)

      result = described_class.create_temporary_loan(offer, contract_date, first_payment_date, payment_frequency, unified_id)

      expect(result).to eq('sample data')
      expect_api_event_record(name: 'loanpro_create_temporary_loan')
    end

    it 'logs the error' do
      allow(Rails.logger).to receive(:error).and_call_original
      response_hash = { status: 400, body: 'bad request error' }
      bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
      stub_request(:post, "#{config[:base_url]}/odata.svc/Loans")
        .with(body: temporary_loan_body.to_json)
        .to_raise(bad_request_error)

      expect do
        described_class.create_temporary_loan(offer, contract_date, first_payment_date, payment_frequency, unified_id)
      end.to raise_error(StandardError)

      expect(Rails.logger).to have_received(:error).thrice

      event = expect_api_event_record(name: 'loanpro_create_temporary_loan')
      expect(event.response).to eq('bad request error')
      expect(event.metadata).to match('status_code' => 400)
    end
  end

  describe '.create_temporary_loan_body' do
    let(:temporary_loan_body) do
      body = super()
      described_class.send(:apply_ignore_warnings, body)
    end

    it 'returns the complete request body used to create a new temporary loan' do
      request_body = described_class.create_temporary_loan_body(offer, contract_date, first_payment_date, payment_frequency, unified_id)

      expect(request_body).to eq(temporary_loan_body)
    end
  end

  describe '.activate_loan' do
    it 'sends the correct request to activate the loan' do
      stub_request(:post, "#{config[:base_url]}/Loans(#{loanpro_id})/AutoPal.Activate()")
        .to_return(body: response_body, headers: response_headers)

      result = described_class.activate_loan(loanpro_id)

      expect(result).to eq('sample data')
      expect_api_event_record(name: 'loanpro_activate_loan')
    end

    context 'when the API response is not successful' do
      let(:loanpro_id) { 'bad_id' }
      let(:failed_response_body) { 'sample error message' }
      let(:response_hash) do
        {
          body: failed_response_body,
          status: 400
        }
      end

      let(:bad_request) do
        Faraday::BadRequestError.new(failed_response_body, response_hash)
      end

      before do
        allow(Rails.logger).to receive(:error).and_call_original
        stub_request(:post, "#{config[:base_url]}/Loans(#{loanpro_id})/AutoPal.Activate()")
          .to_raise(bad_request)
      end

      it 'raises an exception with the correct message' do
        expect { described_class.activate_loan(loanpro_id) }.to raise_error(Clients::LoanproApi::Error) do |error|
          expect(error.wrapped_exception).to be_a(Faraday::BadRequestError)
          expect(error.message).to eq({ response_body: failed_response_body,
                                        response_status: 400,
                                        klass: 'Faraday::BadRequestError',
                                        message: failed_response_body })
        end

        expect(Rails.logger).to have_received(:error).thrice

        event = expect_api_event_record(name: 'loanpro_activate_loan')
        expect(event.response).to eq(failed_response_body)
        expect(event.metadata).to match('loanpro_id' => loanpro_id, 'status_code' => 400)
      end
    end
  end

  describe '.update_loan_settings_custom_fields' do
    let(:loan_settings_id) { 74_283 }
    let(:request_body) do
      {
        LoanSettings: {
          __id: loan_settings_id,
          __update: true,
          CustomFieldValues: { results: custom_fields.body }
        }
      }
    end
    let(:custom_fields) do
      custom_fields = { checking_account_entity_ids: '123,321' }
      Clients::LoanproApi::CustomFields.new(custom_fields:)
    end

    it 'sends the correct request to update custom fields for the loan settings' do
      stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_id})")
        .with(body: request_body.to_json)
        .to_return(body: response_body, headers: response_headers)

      result = described_class.update_loan_settings_custom_fields(loanpro_id:, loan_settings_id:, custom_fields:)

      expect(result).to eq('sample data')
      event = expect_api_event_record(name: 'loanpro_update_loan_settings_custom_fields')
      expect(event.metadata).to match(hash_including('loanpro_id' => loanpro_id, 'loan_settings_id' => loan_settings_id))
    end

    context 'when the API response is not successful' do
      let(:loanpro_id) { 'bad_id' }
      let(:failed_response_body) { 'sample error message' }
      let(:response_hash) do
        {
          body: failed_response_body,
          status: 400
        }
      end

      let(:bad_request) do
        Faraday::BadRequestError.new(failed_response_body, response_hash)
      end

      before do
        allow(Rails.logger).to receive(:error).and_call_original
        stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_id})")
          .to_raise(bad_request)
      end

      it 'raises an exception with the correct message' do
        expect { described_class.update_loan_settings_custom_fields(loanpro_id:, loan_settings_id:, custom_fields:) }.to raise_error(Clients::LoanproApi::Error) do |error|
          expect(error.wrapped_exception).to be_a(Faraday::BadRequestError)
          expect(error.message).to eq({ response_body: failed_response_body,
                                        response_status: 400,
                                        klass: 'Faraday::BadRequestError',
                                        message: failed_response_body })
        end

        expect(Rails.logger).to have_received(:error).thrice

        event = expect_api_event_record(name: 'loanpro_update_loan_settings_custom_fields')
        expect(event.response).to eq(failed_response_body)
        expect(event.metadata).to match('status_code' => 400, 'loanpro_id' => loanpro_id, 'loan_settings_id' => loan_settings_id)
      end
    end
  end

  describe '.deactivate_loan' do
    it 'sends the correct request to deactivate the loan' do
      stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_id})")
        .to_return(body: response_body, headers: response_headers)

      result = described_class.deactivate_loan(loanpro_id, loanpro_setup_id)

      expect(result).to eq('sample data')
      expect_api_event_record(name: 'loanpro_deactivate_loan')
    end

    it 'logs the error' do
      allow(Rails.logger).to receive(:error).and_call_original
      response_hash = { status: 400, body: 'bad request error' }
      bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
      stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_id})")
        .to_raise(bad_request_error)

      expect do
        described_class.deactivate_loan(loanpro_id, loanpro_setup_id)
      end.to raise_error(StandardError)

      expect(Rails.logger).to have_received(:error).thrice

      event = expect_api_event_record(name: 'loanpro_deactivate_loan')
      expect(event.response).to eq('bad request error')
      expect(event.metadata).to match('status_code' => 400, 'loanpro_id' => loanpro_id, 'loanpro_setup_id' => loanpro_setup_id)
    end
  end

  describe '.fetch_loan_details' do
    it 'includes only LoanSetup data along with the loan details by default' do
      stub_request(:get, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_id})?$expand=LoanSetup&nopaging")
        .to_return(body: response_body, headers: response_headers)

      result = described_class.fetch_loan_details(loanpro_id)

      expect(result).to eq('sample data')
      expect_api_event_record(name: 'loanpro_fetch_loan_details')
    end

    it 'includes additional associations along with the loan details when requested' do
      stub_request(:get, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_id})?$expand=LoanSetup,Customers,Portfolios&nopaging")
        .to_return(body: response_body, headers: response_headers)

      result = described_class.fetch_loan_details(loanpro_id, %w[LoanSetup Customers Portfolios])

      expect(result).to eq('sample data')
    end

    it 'logs the error' do
      allow(Rails.logger).to receive(:error).and_call_original
      response_hash = { status: 400, body: 'bad request error' }
      bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
      stub_request(:get, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_id})?$expand=LoanSetup&nopaging")
        .to_raise(bad_request_error)

      expect do
        described_class.fetch_loan_details(loanpro_id)
      end.to raise_error(StandardError)

      expect(Rails.logger).to have_received(:error).thrice

      event = expect_api_event_record(name: 'loanpro_fetch_loan_details')
      expect(event.response).to eq('bad request error')
      expect(event.metadata).to match('status_code' => 400, 'loanpro_id' => loanpro_id)
    end
  end

  describe '.fetch_primary_payment_profile' do
    let(:loanpro_customer_id) { Faker::Number.number(digits: 5) }
    let(:response_payload) { { results: [{ id: Faker::Number.number(digits: 5), type: 'paymentAccount.type.checking' }] } }
    let(:response_body) { { d: response_payload }.to_json }

    it 'sends the correct request to retrieve the primary payment profile for the specified customer' do
      stub_request(:get, "#{config[:base_url]}/odata.svc/Customers(#{loanpro_customer_id})/PaymentAccounts?all&$filter=isPrimary eq 1")
        .to_return(body: response_body, headers: response_headers)

      result = described_class.fetch_primary_payment_profile(loanpro_customer_id)

      expect(result).to eq(response_payload.as_json)
      expect_api_event_record(name: 'loanpro_fetch_primary_payment_profile')
    end

    it 'sends expands as part of the path when present' do
      stub_request(:get, "#{config[:base_url]}/odata.svc/Customers(#{loanpro_customer_id})/PaymentAccounts?$expand=CheckingAccount,CreditCard&$filter=isPrimary%20eq%201&all&nopaging")
        .to_return(body: response_body, headers: response_headers)

      result = described_class.fetch_primary_payment_profile(loanpro_customer_id, %w[CheckingAccount CreditCard])

      expect(result).to eq(response_payload.as_json)
      expect_api_event_record(name: 'loanpro_fetch_primary_payment_profile')
    end

    it 'logs the error' do
      allow(Rails.logger).to receive(:error).and_call_original
      response_hash = { status: 400, body: 'bad request error' }
      bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
      stub_request(:get, "#{config[:base_url]}/odata.svc/Customers(#{loanpro_customer_id})/PaymentAccounts?all&$filter=isPrimary eq 1")
        .to_raise(bad_request_error)

      expect do
        described_class.fetch_primary_payment_profile(loanpro_customer_id)
      end.to raise_error(StandardError)

      expect(Rails.logger).to have_received(:error).thrice

      event = expect_api_event_record(name: 'loanpro_fetch_primary_payment_profile')
      expect(event.response).to eq('bad request error')
      expect(event.metadata).to match('status_code' => 400, 'loanpro_customer_id' => loanpro_customer_id)
    end
  end

  describe '.create_customer' do
    let(:new_customer) do
      Clients::LoanproApi::NewCustomer.new(
        bank_account_token: SecureRandom.uuid,
        bank_account_type: %w[checking savings].sample,
        city: Faker::Address.city,
        date_of_birth: Faker::Date.birthday,
        email: Faker::Internet.email,
        first_name: Faker::Name.first_name,
        last_name: Faker::Name.last_name,
        phone_number: Faker::Number.number(digits: 10).to_s,
        ssn: Faker::Number.number(digits: 9).to_s,
        state: Faker::Address.state_abbr,
        street_address: Faker::Address.street_address,
        unified_id: Faker::Number.number(digits: 8).to_s,
        zip_code: Faker::Number.number(digits: 5).to_s
      )
    end
    let(:response_payload) { { id: 10_035, customId: '10035', firstName: 'ERICA', lastName: 'LAMBERT' } }
    let(:response_body) { { d: response_payload }.to_json }
    let(:create_customer_request) { stub_request(:post, "#{config[:base_url]}/odata.svc/Customers") }

    it 'triggers a request to the LoanPro customer creation API endpoint' do
      create_customer_request.to_return(body: response_body, headers: response_headers)

      result = described_class.create_customer(new_customer)

      expect(result).to eq(response_payload.as_json)
      expect(create_customer_request).to have_been_requested
      expect_api_event_record(name: 'loanpro_create_customer')
    end

    it 'logs the error' do
      allow(Rails.logger).to receive(:error).and_call_original
      response_hash = { status: 400, body: 'bad request error' }
      conflict_error = Faraday::ConflictError.new('conflict', response_hash)
      create_customer_request.to_raise(conflict_error)

      expect { described_class.create_customer(new_customer) }.to raise_error(StandardError)

      expect(Rails.logger).to have_received(:error).thrice

      event = expect_api_event_record(name: 'loanpro_create_customer')
      expect(event.response).to eq('bad request error')
      expect(event.metadata).to match('status_code' => 400, 'unified_id' => new_customer.unified_id)
    end

    context 'body' do
      let(:expected_body) do
        body = {
          status: 'Active',
          customerType: 'customer.type.individual',
          customerIdType: 'customer.idType.ssn',
          gender: 'customer.gender.unknown',
          generationCode: 'customer.generationCode.none',
          Phones: {
            results: [
              {
                # Loanpro throws duplicate warnings
                __ignoreWarnings: true,
                phone: new_customer.phone_number,
                isPrimary: '1',
                isSecondary: '0',
                delete: false,
                _index: 0,
                type: 'customer.phoneType.cell',
                __isDirty: false,
                carrierVerified: 1,
                __lookupInProgress: true,
                carrierName: '',
                isLandLine: 0
              }
            ]
          },
          PrimaryAddress: {
            # Loanpro throws duplicate warnings
            __ignoreWarnings: true,
            country: 'company.country.usa',
            address1: new_customer.street_address,
            zipcode: new_customer.zip_code,
            city: new_customer.city,
            state: new_customer.state,
            verify: true
          },
          MailAddress: {
            # Loanpro throws duplicate warnings
            __ignoreWarnings: true,
            country: 'company.country.usa',
            address1: new_customer.street_address,
            zipcode: new_customer.zip_code,
            city: new_customer.city,
            state: new_customer.state,
            verify: true
          },
          # Loanpro throws duplicate warnings
          __ignoreWarnings: true,
          birthDate: new_customer.date_of_birth,
          firstName: new_customer.first_name,
          lastName: new_customer.last_name,
          ssn: new_customer.ssn,
          email: new_customer.email,
          PaymentAccounts: {
            results: [
              {
                CheckingAccount: {
                  accountType: new_customer.bank_account_type,
                  token: new_customer.bank_account_token
                },
                active: 1,
                isPrimary: 1,
                isSecondary: 0,
                title: "Personal Account #{new_customer.unified_id}",
                type: 'paymentAccount.type.checking'
              }
            ]
          }
        }
        described_class.send(:apply_ignore_warnings, body)
      end

      it 'includes the appropriate request body in this request' do
        create_customer_request.with(body: expected_body.to_json).to_return(body: response_body, headers: response_headers)

        described_class.create_customer(new_customer)

        expect(create_customer_request).to have_been_requested
      end

      it 'includes the appropriate request body in this request for production' do
        allow(Rails.env).to receive(:production?).and_return(true)
        create_customer_request.with(body: expected_body.to_json).to_return(body: response_body, headers: response_headers)

        described_class.create_customer(new_customer)

        expect(create_customer_request).to have_been_requested
      end
    end
  end

  describe '.update_finalized_loan_body' do
    let(:update_params) do
      {
        loanpro_loan_external_id: Faker::Number.number(digits: 5),
        loanpro_customer_external_id: Faker::Number.number(digits: 5),
        max_interest: Faker::Number.decimal,
        loan_setup_id: Faker::Number.number(digits: 5),
        portfolio_ids: [1, 11, 101],
        subportfolio_ids: [3, 33, 131]
      }
    end

    it 'builds the appropriate request body payload' do
      result = described_class.update_finalized_loan_body(update_params)

      expected_body = {
        id: update_params[:loanpro_loan_external_id],
        Customers: {
          results: [
            {
              __id: update_params[:loanpro_customer_external_id],
              __setLoanRole: 'loan.customerRole.primary'
            }
          ]
        },
        __id: update_params[:loanpro_loan_external_id],
        LoanSetup: {
          maxInterestAmount: update_params[:max_interest],
          __id: update_params[:loan_setup_id],
          __update: true
        },
        Portfolios: { results: [{ __id: 1 }, { __id: 11 }, { __id: 101 }] },
        SubPortfolios: { results: [{ __id: 3 }, { __id: 33 }, { __id: 131 }] },
        __update: true,
        __temporary: false
      }

      expect(result).to eq(expected_body)
    end
  end

  describe '.update_finalized_Loan' do
    let(:update_params) do
      {
        loanpro_loan_external_id: Faker::Number.number(digits: 5),
        loanpro_customer_external_id: Faker::Number.number(digits: 5),
        max_interest: Faker::Number.decimal,
        loan_setup_id: Faker::Number.number(digits: 5),
        portfolio_ids: [1, 11, 101],
        subportfolio_ids: [3, 33, 131]
      }
    end

    let(:response_payload) { { id: 29_420, displayId: '********', setupId: 29_303 } }
    let(:response_body) { { d: response_payload }.to_json }

    let(:expected_request_body) do
      {
        id: update_params[:loanpro_loan_external_id],
        Customers: {
          results: [
            {
              __id: update_params[:loanpro_customer_external_id],
              __setLoanRole: 'loan.customerRole.primary'
            }
          ]
        },
        __id: update_params[:loanpro_loan_external_id],
        LoanSetup: {
          maxInterestAmount: update_params[:max_interest],
          __id: update_params[:loan_setup_id],
          __update: true
        },
        Portfolios: { results: [{ __id: 1 }, { __id: 11 }, { __id: 101 }] },
        SubPortfolios: { results: [{ __id: 3 }, { __id: 33 }, { __id: 131 }] },
        __update: true,
        __temporary: false
      }
    end

    it 'triggers a request to the LoanPro to fianlize loan details' do
      stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{update_params[:loanpro_loan_external_id]})")
        .with(body: expected_request_body)
        .to_return(body: response_body, headers: response_headers)

      result = described_class.update_finalized_loan(update_params)

      expect(result).to eq(response_payload.as_json)
      expect_api_event_record(name: 'loanpro_update_finalized_loan')
    end

    it 'logs the error' do
      allow(Rails.logger).to receive(:error).and_call_original
      response_hash = { status: 400, body: 'bad request error' }
      bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
      stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{update_params[:loanpro_loan_external_id]})")
        .with(body: expected_request_body)
        .to_raise(bad_request_error)

      expect do
        described_class.update_finalized_loan(update_params)
      end.to raise_error(StandardError)

      expect(Rails.logger).to have_received(:error).thrice

      event = expect_api_event_record(name: 'loanpro_update_finalized_loan')
      expect(event.response).to eq('bad request error')
      expect(event.metadata).to match('status_code' => 400, 'loanpro_loan_external_id' => update_params[:loanpro_loan_external_id], 'loan_setup_id' => update_params[:loan_setup_id])
    end
  end

  describe '.link_customer_to_loan_body' do
    let(:loanpro_loan_id) { Faker::Number.number(digits: 5) }
    let(:loanpro_customer_id) { Faker::Number.number(digits: 5) }

    it 'builds the appropriate request body payload' do
      result = described_class.link_customer_to_loan_body(loanpro_loan_id, loanpro_customer_id)

      expected_body = {
        id: loanpro_loan_id,
        Customers: {
          results: [
            {
              __id: loanpro_customer_id,
              __setLoanRole: 'loan.customerRole.primary'
            }
          ]
        },
        __update: true,
        __id: loanpro_loan_id
      }
      expect(result).to eq(expected_body)
    end
  end

  describe '.assign_max_interest_body' do
    let(:max_interest_amount) { Faker::Number.decimal }
    let(:loanpro_loan_setup_id) { Faker::Number.number(digits: 5) }

    it 'builds the appropriate request body payload' do
      result = described_class.assign_max_interest_body(max_interest_amount, loanpro_loan_setup_id)

      expected_body = {
        LoanSetup: {
          maxInterestAmount: max_interest_amount.to_f,
          __id: loanpro_loan_setup_id,
          __update: true
        }
      }
      expect(result).to eq(expected_body)
    end
  end

  describe '.assign_portfolios_body' do
    let(:portfolio_ids) { [1, 11, 101] }
    let(:subportfolio_ids) { [3, 33, 131] }

    it 'builds the appropriate request body payload' do
      result = described_class.assign_portfolios_body(portfolio_ids, subportfolio_ids)

      expected_body = {
        Portfolios: { results: [{ __id: 1 }, { __id: 11 }, { __id: 101 }] },
        SubPortfolios: { results: [{ __id: 3 }, { __id: 33 }, { __id: 131 }] }
      }
      expect(result).to eq(expected_body)
    end
  end

  context 'when creating autopays' do
    let(:new_autopay) do
      Clients::LoanproApi::NewAutopay.new(
        ba_processor: 4,
        first_payment_date: Faker::Date.forward(days: 10),
        last_day_of_month_enabled: [true, false].sample,
        number_of_payments: (48...155).to_a.sample,
        payment_amount: Faker::Number.decimal,
        payment_method_account_type: %w[checking savings].sample,
        payment_profile_id: Faker::Number.number(digits: 5),
        process_date: Faker::Date.forward(days: 10),
        recurring_frequency: %w[monthly bi_weekly semi_monthly weekly].sample,
        source_type: %w[WEB BEYOND GDS OTHER].sample
      )
    end
    let(:expected_request_body) do
      {
        Autopays: {
          results: [
            {
              name: 'Scheduled Payment',
              type: 'autopay.type.recurring',
              paymentExtraTowards: 'payment.extra.tx.classicv1',
              amountType: 'autopay.amountType.static',
              amount: new_autopay.payment_amount,
              paymentType: 1,
              chargeServiceFee: '0',
              processCurrent: 1,
              retryDays: 0,
              processTime: 19,
              postPaymentUpdate: 1,
              applyDate: new_autopay.first_payment_date,
              processDate: new_autopay.process_date,
              methodType: 'autopay.methodType.echeck',
              recurringFrequency: new_autopay.recurring_frequency,
              recurringDateOption: 'autopay.recurringDate.applyDate',
              daysInPeriod: '',
              schedulingType: 'autopay.schedulingType.bankingDayPrior',
              processDateCondition: 'bankingDays',
              payoffAdjustment: 1,
              chargeOffRecovery: 0,
              paymentMethodAuthType: new_autopay.payment_method_auth_type,
              paymentMethodAccountType: new_autopay.payment_method_account_type,
              processZeroOrNegativeBalance: 0,
              applyLastDayOfMonthEnabled: new_autopay.last_day_of_month_enabled ? 1 : 0,
              PrimaryPaymentMethod: {
                __metadata: {
                  uri: "http://loanpro.simnang.com/api/public/api/1/odata.svc/PaymentAccounts(id=#{new_autopay.payment_profile_id})",
                  type: 'Entity.PaymentAccount'
                },
                id: new_autopay.payment_profile_id,
                type: 'paymentAccount.type.checking'
              },
              recurringPeriods: new_autopay.number_of_payments + 5,
              baProcessor: new_autopay.ba_processor,
              processDateTime: "#{new_autopay.process_date} 19:00:00",
              PaymentType: {
                __metadata: {
                  type: 'Entity.CustomPaymentType',
                  uri: '/api/1/odata.svc/CustomPaymentTypes(id=1)'
                }
              }
            }
          ]
        }
      }
    end

    describe '.create_autopay_body' do
      it 'builds the appropriate request body payload' do
        result = described_class.create_autopay_body(new_autopay)

        expect(result).to eq(expected_request_body)
      end
    end

    describe '.create_autopay' do
      let(:loanpro_loan_id) { Faker::Number.number(digits: 5) }
      let(:response_payload) { { id: loanpro_loan_id, displayId: '********', setupId: 29_303 } }
      let(:response_body) { { d: response_payload } }

      it 'triggers a request to LoanPro to set the max interest amount for the loan' do
        stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
          .with(body: expected_request_body.to_json)
          .to_return(body: response_body.to_json, headers: response_headers)

        result = described_class.create_autopay(loanpro_loan_id, new_autopay)

        expect(result).to eq(response_payload.as_json)
        event = expect_api_event_record(name: 'loanpro_create_autopay')
        expect(event.response).to eq(response_body.as_json)
        expect(event.metadata).to match(hash_including('status_code' => 200))
      end

      it 'logs the error' do
        allow(Rails.logger).to receive(:error).and_call_original
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
          .with(body: expected_request_body.to_json)
          .to_raise(bad_request_error)

        expect do
          described_class.create_autopay(loanpro_loan_id, new_autopay)
        end.to raise_error(StandardError)

        expect(Rails.logger).to have_received(:error).thrice

        event = expect_api_event_record(name: 'loanpro_create_autopay')
        expect(event.response).to eq('bad request error')
        expect(event.metadata).to match(hash_including('status_code' => 400))
      end

      context 'when the error is a TimeoutError error' do
        let(:exception_instance) { Faraday::TimeoutError.new }
        before do
          stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
            .to_raise(exception_instance)
        end

        it 'raises a TimeoutError' do
          expect { described_class.create_autopay(loanpro_loan_id, new_autopay) }.to raise_error(Clients::LoanproApi::TimeoutError)
        end
      end
    end

    describe '.get_payments' do
      let(:loanpro_loan_id) { Faker::Number.number(digits: 5) }
      let(:response_payload) { { 'results' => [{ 'id' => 11, 'paymentId' => 778 }, { 'id' => 12, 'paymentId' => 0 }] } }
      let(:response_body) { { d: response_payload } }

      it 'triggers a request to LoanPro to get the payments' do
        stub_request(:get, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})/Payments?nopaging=true")
          .to_return(body: response_body.to_json, headers: response_headers)

        result = described_class.get_payments(loanpro_loan_id)
        expect(result).to eq(response_payload.as_json)
        event = expect_api_event_record(name: 'loanpro_get_payments')
        expect(event.response).to eq(response_body.as_json)
        expect(event.metadata).to match(hash_including('status_code' => 200))
      end

      it 'logs the error' do
        allow(Rails.logger).to receive(:error).and_call_original
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:get, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})/Payments?nopaging=true")
          .to_raise(bad_request_error)

        expect do
          described_class.get_payments(loanpro_loan_id)
        end.to raise_error(StandardError)

        expect(Rails.logger).to have_received(:error).thrice

        event = expect_api_event_record(name: 'loanpro_get_payments')
        expect(event.response).to eq('bad request error')
        expect(event.metadata).to match(hash_including('status_code' => 400))
      end
    end

    describe '.get_loan' do
      let(:loanpro_loan_id) { Faker::Number.number(digits: 5) }
      let(:response_payload) do
        {
          'results' => [
            {
              'id' => 307_644,
              'loanId' => 266_986,
              'name' => 'Scheduled Payment',
              'type' => 'autopay.type.recurring',
              'applyDate' => '/Date(1750550400)/',
              'processDateTime' => '/Date(1750446000)/',
              'originalProcessDateTime' => '/Date(1750446000)/',
              'amountType' => 'autopay.amountType.static',
              'amount' => 351.73,
              'status' => 'autopay.status.pending',
              'active' => 1,
              'created' => '/Date(**********)/'
            }
          ]
        }
      end
      let(:response_body) { { d: response_payload } }

      it 'triggers a request to LoanPro to get the loan' do
        stub_request(:get, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})?nopaging=true")
          .to_return(body: response_body.to_json, headers: response_headers)

        result = described_class.get_loan(loanpro_loan_id)
        expect(result).to eq(response_payload.as_json)
        event = expect_api_event_record(name: 'loanpro_get_loan')
        expect(event.response).to eq(response_body.as_json)
        expect(event.metadata).to match(hash_including('status_code' => 200))
      end

      it 'logs the error' do
        allow(Rails.logger).to receive(:error).and_call_original
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:get, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})?nopaging=true")
          .to_raise(bad_request_error)

        expect do
          described_class.get_loan(loanpro_loan_id)
        end.to raise_error(StandardError)

        expect(Rails.logger).to have_received(:error).thrice

        event = expect_api_event_record(name: 'loanpro_get_loan')
        expect(event.response).to eq('bad request error')
        expect(event.metadata).to match(hash_including('status_code' => 400))
      end
    end

    describe '.get_transactions' do
      let(:loanpro_loan_id) { Faker::Number.number(digits: 5) }
      let(:response_payload) { { 'results' => [{ 'id' => 11, 'paymentId' => 778 }, { 'id' => 12, 'paymentId' => 0 }] } }
      let(:response_body) { { d: response_payload } }

      it 'triggers a request to LoanPro to get the payments' do
        stub_request(:get, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})/Transactions?nopaging=true")
          .to_return(body: response_body.to_json, headers: response_headers)

        result = described_class.get_transactions(loanpro_loan_id)
        expect(result).to eq(response_payload.as_json)
        event = expect_api_event_record(name: 'loanpro_get_transactions')
        expect(event.response).to eq(response_body.as_json)
        expect(event.metadata).to match(hash_including('status_code' => 200))
      end

      it 'logs the error' do
        allow(Rails.logger).to receive(:error).and_call_original
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:get, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})/Transactions?nopaging=true")
          .to_raise(bad_request_error)

        expect do
          described_class.get_transactions(loanpro_loan_id)
        end.to raise_error(StandardError)

        expect(Rails.logger).to have_received(:error).thrice

        event = expect_api_event_record(name: 'loanpro_get_transactions')
        expect(event.response).to eq('bad request error')
        expect(event.metadata).to match(hash_including('status_code' => 400))
      end
    end

    describe '.update_checklist' do
      let(:loanpro_loan_id) { 74_283 }
      let(:item_id) { 81 }
      let(:value) { 1 }
      let(:request_body) do
        {
          ChecklistItemValues: {
            results: [
              {
                checklistItemId: item_id,
                checklistItemValue: value,
                statusId: nil
              }
            ]
          }
        }
      end
      let(:response_payload) { { id: loanpro_loan_id, displayId: '********', settingsId: 29_303 } }
      let(:response_body) { { d: response_payload } }

      it 'sends the correct request to update checklist' do
        stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
          .with(body: request_body.to_json)
          .to_return(body: response_body.to_json, headers: response_headers)

        result = described_class.update_checklist(loanpro_loan_id:, item_id:, value:)

        expect(result).to eq(response_payload.as_json)
        event = expect_api_event_record(name: 'loanpro_update_checklist')
        expect(event.response).to eq(response_body.as_json)
        expect(event.metadata).to match(hash_including('loanpro_loan_id' => loanpro_loan_id, 'item_id' => item_id, 'value' => value))
      end

      context 'when the request fails due to a bad request error' do
        it 'logs the error' do
          allow(Rails.logger).to receive(:error).and_call_original
          response_hash = { status: 400, body: 'bad request error' }
          bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
          stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
            .with(body: request_body.to_json)
            .to_raise(bad_request_error)

          expect do
            described_class.update_checklist(loanpro_loan_id:, item_id:, value:)
          end.to raise_error(StandardError)

          expect(Rails.logger).to have_received(:error).thrice

          event = expect_api_event_record(name: 'loanpro_update_checklist')
          expect(event.response).to eq('bad request error')
          expect(event.metadata).to match(hash_including('loanpro_loan_id' => loanpro_loan_id, 'item_id' => item_id, 'value' => value, 'status_code' => 400))
        end
      end
    end

    describe '.get_autopay' do
      let(:autopay_id) { Faker::Number.number(digits: 5) }
      let(:response_payload) do
        {
          'results' => [
            {
              'id' => 307_644,
              'loanId' => 266_986,
              'name' => 'Scheduled Payment',
              'type' => 'autopay.type.single',
              'status' => 'autopay.status.pending',
              'created' => '/Date(**********)/'
            }
          ]
        }
      end
      let(:response_body) { { d: response_payload } }

      it 'triggers a request to LoanPro to get the autopay' do
        stub_request(:get, "#{config[:base_url]}/odata.svc/Autopays(#{autopay_id})?nopaging=true")
          .to_return(body: response_body.to_json, headers: response_headers)

        result = described_class.get_autopay(autopay_id)
        expect(result).to eq(response_payload.as_json)
        event = expect_api_event_record(name: 'loanpro_get_autopay')
        expect(event.response).to eq(response_body.as_json)
        expect(event.metadata).to match(hash_including('status_code' => 200))
      end

      it 'logs the error' do
        allow(Rails.logger).to receive(:error).and_call_original
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:get, "#{config[:base_url]}/odata.svc/Autopays(#{autopay_id})?nopaging=true")
          .to_raise(bad_request_error)

        expect do
          described_class.get_autopay(autopay_id)
        end.to raise_error(StandardError)

        expect(Rails.logger).to have_received(:error).thrice

        event = expect_api_event_record(name: 'loanpro_get_autopay')
        expect(event.response).to eq('bad request error')
        expect(event.metadata).to match(hash_including('status_code' => 400))
      end
    end

    describe '.cancel_autopay' do
      let(:loanpro_loan_id) { Faker::Number.number(digits: 5) }
      let(:autopay_id) { Faker::Number.number(digits: 5) }

      let(:request_body) do
        {
          Autopays: {
            results: [
              {
                id: autopay_id,
                status: 'autopay.status.cancelled',
                __update: true
              }
            ]
          }
        }
      end

      let(:response_payload) { { 'id' => 307_644, 'loanId' => 266_986 } }
      let(:response_body) { { d: response_payload } }

      it 'triggers a request to the LoanPro to fianlize loan details' do
        stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
          .with(body: request_body.to_json)
          .to_return(body: response_body.to_json, headers: response_headers)

        result = described_class.cancel_autopay(loanpro_loan_id, autopay_id)
        expect(result).to eq(response_payload.as_json)
        expect_api_event_record(name: 'loanpro_cancel_autopay')
      end

      it 'logs the error' do
        allow(Rails.logger).to receive(:error).and_call_original
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
          .with(body: request_body.to_json)
          .to_raise(bad_request_error)

        expect do
          described_class.cancel_autopay(loanpro_loan_id, autopay_id)
        end.to raise_error(StandardError)

        expect(Rails.logger).to have_received(:error).thrice

        event = expect_api_event_record(name: 'loanpro_cancel_autopay')
        expect(event.response).to eq('bad request error')
        expect(event.metadata).to match(hash_including('status_code' => 400))
      end
    end
  end

  describe '.create_cip_autopay' do
    let(:loanpro_loan_id) { Faker::Number.number(digits: 5) }
    let(:response_payload) { { id: loanpro_loan_id, displayId: '********', setupId: 29_303 } }
    let(:cip_autopay) do
      {
        amount: 100.to_d,
        amountType: 'static',
        applyDate: '2025-04-11',
        lastDayOfMonthEnabled: 0,
        methodType: 'autopay.methodType.echeck',
        name: 'Customer Initiated Payment',
        paymentExtraTowards: 'payment.extra.tx.classicv1',
        paymentMethodAccountType: 'bankacct.type.checking',
        paymentMethodAuthType: 'payment.echeckauth.WEB',
        paymentType: 1,
        recurringFrequency: 'autopay.recurringFrequency.single',
        type: 'autopay.type.single'
      }
    end
    let(:expected_request_body) do
      {
        Autopays: {
          results: [
            cip_autopay
          ]
        }
      }
    end
    let(:response_body) { { d: response_payload } }

    it 'triggers a request to LoanPro to create a new customer initiated payment' do
      stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
        .with(body: expected_request_body.to_json)
        .to_return(body: response_body.to_json, headers: response_headers)

      result = described_class.create_cip_autopay(loanpro_loan_id, cip_autopay)

      expect(result).to eq(response_payload.as_json)
      event = expect_api_event_record(name: 'loanpro_create_cip_autopay')
      expect(event.response).to eq(response_body.as_json)
      expect(event.metadata).to match(hash_including('status_code' => 200))
    end

    it 'logs the error' do
      allow(Rails.logger).to receive(:error).and_call_original
      response_hash = { status: 400, body: 'bad request error' }
      bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
      stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
        .with(body: expected_request_body.to_json)
        .to_raise(bad_request_error)

      expect do
        described_class.create_cip_autopay(loanpro_loan_id, cip_autopay)
      end.to raise_error(StandardError)

      expect(Rails.logger).to have_received(:error).thrice

      event = expect_api_event_record(name: 'loanpro_create_cip_autopay')
      expect(event.response).to eq('bad request error')
      expect(event.metadata).to match(hash_including('status_code' => 400))
    end

    context 'when the error is a TimeoutError error' do
      let(:exception_instance) { Faraday::TimeoutError.new }
      before do
        stub_request(:put, "#{config[:base_url]}/odata.svc/Loans(#{loanpro_loan_id})")
          .to_raise(exception_instance)
      end

      it 'raises a TimeoutError' do
        expect { described_class.create_cip_autopay(loanpro_loan_id, cip_autopay) }.to raise_error(Clients::LoanproApi::TimeoutError)
      end
    end
  end

  describe '.get_active_loanpro_loan_by' do
    let(:unified_id) { Faker::Number.number(digits: 8).to_s }
    let(:response_payload) do
      {
        'results' => [
          {
            'id' => 307_644,
            'loanId' => 266_986,
            'displayId' => unified_id,
            'active' => 1,
            'temporaryAccount' => 0,
            'created' => '/Date(**********)/'
          }
        ],
        'summary' => { 'start' => 0, 'pageSize' => 50, 'total' => 1 }
      }
    end
    let(:query_params) do
      "$filter=displayId eq '#{unified_id}' and temporaryAccount eq 0 and active eq 1 and archived eq 0"
    end

    let(:response_body) { { d: response_payload } }

    it 'triggers a request to LoanPro to return loanpro loans' do
      stub_request(:get, "#{config[:base_url]}/odata.svc/Loans()?#{query_params}")
        .to_return(body: response_body.to_json, headers: response_headers)

      result = described_class.get_active_loanpro_loan_by(unified_id:)
      expect(result).to eq(response_payload.as_json)
      event = expect_api_event_record(name: 'loanpro_get_active_loanpro_loan_by')
      expect(event.response).to eq(response_body.as_json)
      expect(event.metadata).to match(hash_including('status_code' => 200))
    end

    it 'logs the error' do
      allow(Rails.logger).to receive(:error).and_call_original
      response_hash = { status: 400, body: 'bad request error' }
      bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
      stub_request(:get, "#{config[:base_url]}/odata.svc/Loans()?#{query_params}")
        .to_raise(bad_request_error)

      expect do
        described_class.get_active_loanpro_loan_by(unified_id:)
      end.to raise_error(StandardError)

      expect(Rails.logger).to have_received(:error).thrice

      event = expect_api_event_record(name: 'loanpro_get_active_loanpro_loan_by')
      expect(event.response).to eq('bad request error')
      expect(event.metadata).to match(hash_including('status_code' => 400))
    end
  end
end
