##########################################################################################
# Above Lending Platform
#
# This file contains optional environment variables to control your environment.  Defaults
# are saved in this file and is saved in our project.
##########################################################################################

DISABLE_FARADAY_LOGGING=false

# LOCAL CACHE STORE:
# Override to isolate application cache
######
# REDIS_PREFIX=ams
# REDIS_URI=redis://host.docker.internal:6379/0

# LOCAL SIDEKIQ REDIS:
# Override to test sidekiq jobs locally
######
# REDIS_URL=redis://host.docker.internal:6379/1

# DISABLE SIDEKIQ INLINE
# Override inline sidekiq mode to use docker service
######
DISABLE_SIDEKIQ_INLINE=true

# DATADOG IN DEVELOPMENT:
# Enable to allow connection to local datadog agent in development
######
# ENABLE_DATADOG_DEVELOPMENT=true

# DOCUSIGN Debugging
# Force Docusign Debugging mode
######
# DOCUSIGN_CLIENT_DEBUGGING=true

# Handle multiple requests for AMS
WEB_CONCURRENCY=2

######
# LOCAL WEBHOOKS:
# Enable local webhooks simulation in development (not applicable to lower environments).
#
# When enabled, simulates webhooks for specific actions.
# Currently supports creating offers when
#   Clients::GdsApi.get_offers is invoked.
#
# Default is true for development, disable to allow lower environment to handle this functionality
######
ENABLE_LOCAL_WEBHOOKS=true
