# frozen_string_literal: true

class CentralTimeZone
  CENTRAL_TIME_ZONE_NAME = 'Central Time (US & Canada)'

  def self.to_central_time(time)
    time.in_time_zone(CENTRAL_TIME_ZONE_NAME)
  end

  def self.now
    Time.now.in_time_zone(CENTRAL_TIME_ZONE_NAME)
  end

  def self.at_noon
    now.at_noon
  end

  def self.at_2pm
    at_noon + 2.hours
  end

  # The hour in UTC for 3pm central time
  def self.three_pm_utc_hour
    (at_noon + 3.hours).utc.hour
  end

  def self.today
    now.to_date
  end
end
