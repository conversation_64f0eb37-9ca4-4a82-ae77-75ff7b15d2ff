# frozen_string_literal: true

# Payment helper methods to schedule CIP
class PaymentHelper
  PROCEED_WITH_PAYMENT = 2

  def self.time_zone(apply_timezone)
    TZInfo::Timezone.get(apply_timezone)
  end

  def self.default_autopay_attributes
    { **base_attributes, **payment_attributes, **schedule_attributes }
  end

  def self.base_attributes
    {
      name: 'Customer Initiated Payment',
      type: 'autopay.type.single',
      paymentExtraTowards: 'payment.extra.tx.classicv1',
      amountType: 'static',
      chargeServiceFee: 0
    }
  end

  def self.payment_attributes
    {
      processCurrent: true,
      retryDays: 0,
      postPaymentUpdate: 1,
      chargeOffRecovery: 0,
      payoffAdjustment: 1,
      paymentMethodAccountType: 'bankacct.type.checking',
      paymentMethodAuthType: 'payment.echeckauth.WEB',
      paymentType: 1
    }
  end

  def self.schedule_attributes
    {
      recurringFrequency: 'autopay.recurringFrequency.single',
      lastDayOfMonthEnabled: 0,
      daysInPeriod: '',
      processZeroOrNegativeBalance: 0,
      schedulingType: 'autopay.schedulingType.bankingDayPrior',
      processDateCondition: 'bankingDays',
      methodType: 'autopay.methodType.echeck',
      recurringPeriods: 1,
      baProcessor: '1'
    }
  end
end
