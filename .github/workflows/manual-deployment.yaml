name: Manual Deployment Workflow
run-name: Manual Deployment to ${{ inputs.DestEnvironment }}-${{ inputs.AppDevName }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      DestEnvironment:
        type: choice
        description: Destination Environment
        required: true
        options:
          - production
          - sandbox
          - staging
        default: 'sandbox'
      AppDevName:
        type: choice
        description: Destination Environment
        required: true
        options:
          - ams
          - ams-2
        default: 'ams'

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      ecs_env: ${{ steps.step1.outputs.ecs_env }}
    steps:
      - name: Generate runner name
        id: step1
        run: |
          if [ ${{ inputs.DestEnvironment }} == 'stage' ]; then
            echo "ecs_env=staging" >> $GITHUB_OUTPUT
          elif [ ${{ inputs.DestEnvironment }} == 'prod' ]; then
            echo "ecs_env=production" >> $GITHUB_OUTPUT
          else
            echo "ecs_env=${{ inputs.DestEnvironment }}" >> $GITHUB_OUTPUT
          fi

  cd-build:
    uses: ./.github/workflows/cd-build.yaml
    secrets: inherit
    needs: [setup]
    with:
      DestEnvironment: ${{ needs.setup.outputs.ecs_env }}

  cd-deploy-sandbox-ecs:
    if: inputs.DestEnvironment == 'sandbox' && inputs.AppDevName == 'ams'
    uses: ./.github/workflows/cd-deploy-ams-ecs-sandbox.yaml
    needs: [cd-build, setup]
    secrets: inherit
    with:
      DestEnvironment: ${{ needs.setup.outputs.ecs_env }}
      AppDevName: ${{ inputs.AppDevName }}

  cd-deploy-sandbox-2-ecs:
    if: inputs.DestEnvironment == 'sandbox' && inputs.AppDevName == 'ams-2'
    uses: ./.github/workflows/cd-deploy-ams-2-ecs-sandbox.yaml
    needs: [cd-build, setup]
    secrets: inherit
    with:
      DestEnvironment: ${{ needs.setup.outputs.ecs_env }}
      AppDevName: ${{ inputs.AppDevName }}

  cd-deploy-staging-ecs:
    if: needs.setup.outputs.ecs_env == 'staging' && inputs.AppDevName == 'ams'
    uses: ./.github/workflows/cd-deploy-ams-ecs-staging.yaml
    needs: [cd-build, setup]
    secrets: inherit
    with:
      DestEnvironment: ${{ needs.setup.outputs.ecs_env }}
      AppDevName: ${{ inputs.AppDevName }}

  cd-deploy-production-ecs:
    if: needs.setup.outputs.ecs_env == 'production' && inputs.AppDevName == 'ams'
    uses: ./.github/workflows/cd-deploy-ams-ecs-production.yaml
    needs: [cd-build, setup]
    secrets: inherit
    with:
      DestEnvironment: ${{ needs.setup.outputs.ecs_env }}
      AppDevName: ${{ inputs.AppDevName }}
