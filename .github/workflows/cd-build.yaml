name: Continuous Deployment - Image Build ECS

on:
  workflow_call:
    inputs:
      DestEnvironment:
        type: string
        description: Destination Environment
        required: true

env:
  ECR_REPOSITORY: ams

jobs:
  cd-image-build:
    name: Build Container Image
    runs-on: codebuild-${{ inputs.DestEnvironment }}-github-runner-${{ github.run_id }}-${{ github.run_attempt }}
    steps:
      - name: Checkout Code
        id: cd_manual_checkout_code
        uses: actions/checkout@v4

      - name: Set environment variables from AWS Environment into GITHUB_ENV
        id: cd_manual_manual_env_injection
        run: echo "$(printenv |grep AWS)" >> $GITHUB_ENV

      - name: Append AWS_ACCOUNT_ID to GITHUB_ENV
        id: cd_manual_fetch_aws_account_id
        run: |
          echo "AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)" >> $GITHUB_ENV
      - name: Append ECR_REGISTRY to GITHUB_ENV
        id: cd_manual_fetch_set_ecr_var
        run: |
          echo "ECR_REGISTRY=$(echo ${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com)" >> $GITHUB_ENV
      - name: Login to Amazon ECR
        id: cd_manual_login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Check if image ${{ github.sha }} exists in ECR
        id: cd_image_check
        run: |
          set +e
          docker manifest inspect ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY}}:${{ github.sha }}
          RC=$?
          if [[ $RC == 0 ]]; then
            echo "Manifest found, should skip build"
          else
            echo "Manifest not found, should build this image"
          fi
          echo "SKIP_IMAGE_BUILD=$RC" >> "$GITHUB_OUTPUT"
        continue-on-error: true

      - name: Build, tag, and push image to Amazon ECR for ${{ inputs.DestEnvironment }}
        id: cd_manual_build_push
        if: steps.cd_image_check.outputs.SKIP_IMAGE_BUILD != 0
        run: |
          docker build -t ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY}}:${{ github.sha }} . \
            --build-arg GITHUB_TOKEN=${{ secrets.GH_TOKEN }} \
            --build-arg BUNDLE_GEMS__CONTRIBSYS__COM=${{ secrets.BUNDLE_GEMS__CONTRIBSYS__COM }} \
            --build-arg DATADOG_API_KEY=${{ secrets.DATADOG_API_KEY }} \
            --build-arg DD_VERSION=${{ github.sha }}

          docker push ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY}}:${{ github.sha }}
