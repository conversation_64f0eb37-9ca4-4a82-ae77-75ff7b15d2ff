name: AMS2 ECS Deploy - Sandbox

on:
  workflow_call:
    inputs:
      DestEnvironment:
        type: string
        description: Destination Environment
        required: true
      AppDevName:
        type: string
        description: App name with suffix for multi-dev environments (ie ams-2)
        required: true
        default: ams-2
    secrets:
      GH_TOKEN:
        required: true
      SLACK_BOT_TOKEN:
        required: true

env:
  AWS_REGION: us-east-1

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  deploy:
    name: Deployment - ${{ inputs.DestEnvironment }}
    runs-on: codebuild-${{ inputs.DestEnvironment }}-github-runner-${{ github.run_id }}-${{ github.run_attempt }}
    if: inputs.DestEnvironment == 'sandbox'
    environment: ${{ inputs.DestEnvironment }}
    steps:
      - name: Checkout terraform-infra
        uses: actions/checkout@v3
        with:
          repository: Above-Lending/terraform-infra
          token: ${{ secrets.GH_TOKEN }}
          ref: 'main'

      - name: Set environment variables from AWS Environment into GITHUB_ENV
        id: env_injection
        run: echo "$(printenv |grep AWS)" >> $GITHUB_ENV

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Set up Tofu
        uses: opentofu/setup-opentofu@v1
        with:
          tofu_version: ~>1.9

      # Update SSM Parameter with Image Tag
      - name: Update SSM Parameter with Image Tag
        run: |
          aws ssm put-parameter \
            --name "/${{ inputs.DestEnvironment }}/${{ inputs.AppDevName }}/imagetag" \
            --value "${{ github.sha }}" \
            --type "String" \
            --overwrite

      - name: Tofu Init for Migrations
        run: tofu init -input=false -reconfigure
        working-directory: resources/ecs_local_exec/migrations

      - name: Tofu Plan for Migrations
        run: |
          tofu plan -input=false \
            -var="environment=${{ inputs.DestEnvironment }}" \
            -var="app_name=${{ inputs.AppDevName }}" \
            -var="cluster_name=${{ inputs.DestEnvironment }}"
        working-directory: resources/ecs_local_exec/migrations

      - name: Tofu Apply for Migrations
        run: |
          tofu apply -input=false -auto-approve \
            -var="environment=${{ inputs.DestEnvironment }}" \
            -var="app_name=${{ inputs.AppDevName }}" \
            -var="cluster_name=${{ inputs.DestEnvironment }}"
        working-directory: resources/ecs_local_exec/migrations

      # Main App Deployment
      - name: Tofu Init for App
        run: tofu init -input=false -var-file=${{ inputs.DestEnvironment }}.tfvars -reconfigure
        working-directory: resources/ecs_apps/${{ inputs.AppDevName }}

      - name: Tofu Plan for App
        run: |
          tofu plan -input=false \
            -var-file=${{ inputs.DestEnvironment }}.tfvars
        working-directory: resources/ecs_apps/${{ inputs.AppDevName }}

      - name: Tofu Apply for App
        run: |
          tofu apply -input=false -auto-approve \
            -var-file=${{ inputs.DestEnvironment }}.tfvars
        working-directory: resources/ecs_apps/${{ inputs.AppDevName }}

      # Sidekiq Deployment
      - name: Tofu Init for Sidekiq
        run: tofu init -input=false -var-file=${{ inputs.DestEnvironment }}.tfvars -reconfigure
        working-directory: resources/ecs_apps/${{ inputs.AppDevName }}-sidekiq

      - name: Tofu Plan for Sidekiq
        run: |
          tofu plan -input=false \
            -var-file=${{ inputs.DestEnvironment }}.tfvars
        working-directory: resources/ecs_apps/${{ inputs.AppDevName }}-sidekiq

      - name: Tofu Apply for Sidekiq
        run: |
          tofu apply -input=false -auto-approve \
            -var-file=${{ inputs.DestEnvironment }}.tfvars
        working-directory: resources/ecs_apps/${{ inputs.AppDevName }}-sidekiq

      # Wait for both services to be stable
      - name: Wait for services to be stable
        run: |
          aws ecs wait services-stable \
            --cluster ${{ inputs.DestEnvironment }} \
            --services \
              ${{ inputs.DestEnvironment }}-${{ inputs.AppDevName }}-web \
              ${{ inputs.DestEnvironment }}-${{ inputs.AppDevName }}-sidekiq

  notify-slack:
    name: Send Slack Notification
    runs-on: ubuntu-latest
    needs: [deploy]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send message to Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,commit,author,action,eventName,ref
          text: "Success! Deployed to AMS [SANDBOX]"
          author_name: 'GitHub Actions'
          custom_payload: |
            {
              "channel": "#tech-releases",
              "username": "GitHub Action",
              "text": "A new AMS release tag has been published!",
              "attachments": [
                {
                  "color": "#36a64f",
                  "fields": [
                    {
                      "title": "Release Information",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },
                    {
                      "title": "Commit SHA",
                      "value": "${{ github.sha }}",
                      "short": true
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.AMS_ALEN_BOT_WEBHOOK_URL }}
