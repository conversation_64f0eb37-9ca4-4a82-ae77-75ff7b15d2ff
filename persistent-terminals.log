[2025-06-16T15:31:05.203Z] Extension deactivated
[2025-06-16T15:31:21.029Z] Extension activated
[2025-06-16T15:31:21.029Z] Created terminal: dev-bash
[2025-06-16T15:31:21.029Z] Executed command in dev-bash: source bin/setenv development
[2025-06-16T15:31:21.030Z] Executed command in dev-bash: dcrc bundle install
[2025-06-16T15:31:21.030Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-16T15:31:21.030Z] Created terminal: Frontend
[2025-06-16T15:31:21.030Z] Executed command in Frontend: source bin/setenv sandbox
[2025-06-16T15:31:21.030Z] Executed command in Frontend: dcrc bin/rails console
[2025-06-16T15:31:21.030Z] Created terminal: sidekiq
[2025-06-16T15:31:21.030Z] Executed command in sidekiq: source bin/setenv development
[2025-06-16T15:31:21.030Z] Executed command in sidekiq: dcrc bundle exec sidekiq
[2025-06-16T15:31:21.030Z] Created terminal: alu
[2025-06-16T15:31:21.030Z] Executed command in alu: source bin/setenv development
[2025-06-16T15:31:21.030Z] Executed command in alu: dcu alp
[2025-06-16T15:31:21.031Z] Persistent terminals creation completed
[2025-06-16T15:46:22.015Z] Extension deactivated
[2025-06-16T15:46:42.462Z] Extension activated
[2025-06-16T15:46:42.463Z] Created terminal: dev-bash
[2025-06-16T15:46:42.463Z] Executed command in dev-bash: dcrc bundle install
[2025-06-16T15:46:42.463Z] Created terminal: rspec
[2025-06-16T15:46:42.463Z] Executed command in rspec: dcrc bash
[2025-06-16T15:46:42.463Z] Executed command in rspec: bin/rspec
[2025-06-16T15:46:42.464Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-06-16T15:46:42.464Z] Executed command in dev-bash: source bin/setenv development
[2025-06-16T15:46:42.464Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-16T15:46:42.464Z] Created terminal: sandbox-rails console
[2025-06-16T15:46:42.464Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-06-16T15:46:42.464Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-06-16T15:46:42.464Z] Created terminal: sidekiq
[2025-06-16T15:46:42.464Z] Executed command in sidekiq: source bin/setenv development
[2025-06-16T15:46:42.464Z] Executed command in sidekiq: dcrc bundle exec sidekiq
[2025-06-16T15:46:42.464Z] Created terminal: alu
[2025-06-16T15:46:42.465Z] Executed command in alu: source bin/setenv development
[2025-06-16T15:46:42.465Z] Executed command in alu: dcu alp
[2025-06-16T15:46:42.465Z] Persistent terminals creation completed
[2025-06-18T18:15:25.968Z] Extension deactivated
[2025-06-18T18:21:04.701Z] Extension activated
[2025-06-18T18:21:04.702Z] Created terminal: dev-bash
[2025-06-18T18:21:04.702Z] Executed command in dev-bash: dcrc bundle install
[2025-06-18T18:21:04.703Z] Created terminal: rspec
[2025-06-18T18:21:04.703Z] Executed command in rspec: dcrc bash
[2025-06-18T18:21:04.703Z] Executed command in rspec: bin/rspec
[2025-06-18T18:21:04.703Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-06-18T18:21:04.703Z] Executed command in dev-bash: source bin/setenv development
[2025-06-18T18:21:04.703Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-18T18:21:04.703Z] Created terminal: sandbox-rails console
[2025-06-18T18:21:04.703Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-06-18T18:21:04.703Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-06-18T18:21:04.704Z] Created terminal: sidekiq
[2025-06-18T18:21:04.704Z] Executed command in sidekiq: source bin/setenv development
[2025-06-18T18:21:04.704Z] Executed command in sidekiq: dcrc bundle exec sidekiq
[2025-06-18T18:21:04.704Z] Found existing terminal with given terminal name: alu, Skipping creation..
[2025-06-18T18:21:04.704Z] Executed command in alu: source bin/setenv development
[2025-06-18T18:21:04.704Z] Executed command in alu: dcu alp
[2025-06-18T18:21:04.704Z] Persistent terminals creation completed
[2025-06-18T18:21:07.641Z] Extension deactivated
[2025-06-18T18:33:12.929Z] Extension activated
[2025-06-18T18:33:12.930Z] Created terminal: dev-bash
[2025-06-18T18:33:12.931Z] Executed command in dev-bash: dcrc bundle install
[2025-06-18T18:33:12.931Z] Created terminal: rspec
[2025-06-18T18:33:12.931Z] Executed command in rspec: dcrc bash
[2025-06-18T18:33:12.931Z] Executed command in rspec: bin/rspec
[2025-06-18T18:33:12.931Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-06-18T18:33:12.931Z] Executed command in dev-bash: source bin/setenv development
[2025-06-18T18:33:12.931Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-18T18:33:12.931Z] Created terminal: sandbox-rails console
[2025-06-18T18:33:12.931Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-06-18T18:33:12.931Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-06-18T18:33:12.931Z] Created terminal: sidekiq
[2025-06-18T18:33:12.931Z] Executed command in sidekiq: source bin/setenv development
[2025-06-18T18:33:12.931Z] Executed command in sidekiq: dcrc bundle exec sidekiq
[2025-06-18T18:33:12.932Z] Created terminal: alu
[2025-06-18T18:33:12.932Z] Executed command in alu: source bin/setenv development
[2025-06-18T18:33:12.932Z] Executed command in alu: dcu alp
[2025-06-18T18:33:12.932Z] Persistent terminals creation completed
[2025-06-19T18:44:24.846Z] Extension deactivated
[2025-06-19T19:30:03.516Z] Extension activated
[2025-06-19T19:30:03.517Z] Created terminal: rspec
[2025-06-19T19:30:03.518Z] Executed command in rspec: dcrc bash
[2025-06-19T19:30:03.518Z] Executed command in rspec: bin/rspec
[2025-06-19T19:30:03.518Z] Created terminal: dev-bash
[2025-06-19T19:30:03.518Z] Executed command in dev-bash: dcrc bundle install
[2025-06-19T19:30:03.518Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-19T19:30:03.518Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-06-19T19:30:03.518Z] Executed command in dev-bash: source bin/setenv development
[2025-06-19T19:30:03.518Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-19T19:30:03.518Z] Created terminal: alu-dev-env
[2025-06-19T19:30:03.518Z] Executed command in alu-dev-env: source bin/setenv development
[2025-06-19T19:30:03.519Z] Executed command in alu-dev-env: dcu alp
[2025-06-19T19:30:03.519Z] Created terminal: sandbox-rails console
[2025-06-19T19:30:03.519Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-06-19T19:30:03.519Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-06-19T19:30:03.519Z] Persistent terminals creation completed
[2025-06-23T18:58:24.893Z] Extension deactivated
[2025-06-23T18:59:08.920Z] Extension activated
[2025-06-23T18:59:08.920Z] Created terminal: rspec
[2025-06-23T18:59:08.921Z] Executed command in rspec: dcrc bash
[2025-06-23T18:59:08.921Z] Executed command in rspec: bin/rspec
[2025-06-23T18:59:08.921Z] Created terminal: dev-bash
[2025-06-23T18:59:08.921Z] Executed command in dev-bash: dcrc bundle install
[2025-06-23T18:59:08.921Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-23T18:59:08.921Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-06-23T18:59:08.921Z] Executed command in dev-bash: source bin/setenv development
[2025-06-23T18:59:08.921Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-23T18:59:08.921Z] Created terminal: alu-dev-env
[2025-06-23T18:59:08.922Z] Executed command in alu-dev-env: source bin/setenv development
[2025-06-23T18:59:08.922Z] Executed command in alu-dev-env: dcu alp
[2025-06-23T18:59:08.922Z] Created terminal: sandbox-rails console
[2025-06-23T18:59:08.922Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-06-23T18:59:08.922Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-06-23T18:59:08.922Z] Persistent terminals creation completed
[2025-06-27T17:54:48.166Z] Extension deactivated
[2025-06-27T17:55:00.099Z] Extension activated
[2025-06-27T17:55:00.099Z] Found existing terminal with given terminal name: rspec, Skipping creation..
[2025-06-27T17:55:00.099Z] Executed command in rspec: dcrc bash
[2025-06-27T17:55:00.100Z] Executed command in rspec: bin/rspec
[2025-06-27T17:55:00.100Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-06-27T17:55:00.100Z] Executed command in dev-bash: dcrc bundle install
[2025-06-27T17:55:00.100Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-27T17:55:00.100Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-06-27T17:55:00.100Z] Executed command in dev-bash: source bin/setenv development
[2025-06-27T17:55:00.100Z] Executed command in dev-bash: dcrc bin/rails console
[2025-06-27T17:55:00.100Z] Found existing terminal with given terminal name: alu-dev-env, Skipping creation..
[2025-06-27T17:55:00.100Z] Executed command in alu-dev-env: source bin/setenv development
[2025-06-27T17:55:00.100Z] Executed command in alu-dev-env: dcu alp
[2025-06-27T17:55:00.101Z] Found existing terminal with given terminal name: sandbox-rails console, Skipping creation..
[2025-06-27T17:55:00.101Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-06-27T17:55:00.101Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-06-27T17:55:00.101Z] Persistent terminals creation completed
[2025-06-28T14:37:07.205Z] Extension deactivated
[2025-07-01T14:41:24.227Z] Extension activated
[2025-07-01T14:41:24.229Z] Created terminal: rspec
[2025-07-01T14:41:24.229Z] Executed command in rspec: dcrc bash
[2025-07-01T14:41:24.229Z] Executed command in rspec: bin/rspec
[2025-07-01T14:41:24.229Z] Created terminal: dev-bash
[2025-07-01T14:41:24.229Z] Executed command in dev-bash: dcrc bundle install
[2025-07-01T14:41:24.230Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-01T14:41:24.230Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-07-01T14:41:24.230Z] Executed command in dev-bash: source bin/setenv development
[2025-07-01T14:41:24.230Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-01T14:41:24.230Z] Created terminal: alu-dev-env
[2025-07-01T14:41:24.230Z] Executed command in alu-dev-env: source bin/setenv development
[2025-07-01T14:41:24.230Z] Executed command in alu-dev-env: dcu alp
[2025-07-01T14:41:24.230Z] Created terminal: sandbox-rails console
[2025-07-01T14:41:24.231Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-07-01T14:41:24.231Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-07-01T14:41:24.231Z] Persistent terminals creation completed
[2025-07-02T15:10:38.796Z] Extension deactivated
[2025-07-02T15:10:50.546Z] Extension activated
[2025-07-02T15:10:50.547Z] Found existing terminal with given terminal name: rspec, Skipping creation..
[2025-07-02T15:10:50.547Z] Executed command in rspec: dcrc bash
[2025-07-02T15:10:50.547Z] Executed command in rspec: bin/rspec
[2025-07-02T15:10:50.547Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-07-02T15:10:50.547Z] Executed command in dev-bash: dcrc bundle install
[2025-07-02T15:10:50.547Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-02T15:10:50.547Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-07-02T15:10:50.547Z] Executed command in dev-bash: source bin/setenv development
[2025-07-02T15:10:50.547Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-02T15:10:50.548Z] Found existing terminal with given terminal name: alu-dev-env, Skipping creation..
[2025-07-02T15:10:50.548Z] Executed command in alu-dev-env: source bin/setenv development
[2025-07-02T15:10:50.548Z] Executed command in alu-dev-env: dcu alp
[2025-07-02T15:10:50.548Z] Found existing terminal with given terminal name: sandbox-rails console, Skipping creation..
[2025-07-02T15:10:50.548Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-07-02T15:10:50.548Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-07-02T15:10:50.548Z] Persistent terminals creation completed
[2025-07-07T17:21:41.711Z] Extension deactivated
[2025-07-07T17:22:05.105Z] Extension activated
[2025-07-07T17:22:05.106Z] Found existing terminal with given terminal name: rspec, Skipping creation..
[2025-07-07T17:22:05.106Z] Executed command in rspec: dcrc bash
[2025-07-07T17:22:05.106Z] Executed command in rspec: bin/rspec
[2025-07-07T17:22:05.106Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-07-07T17:22:05.106Z] Executed command in dev-bash: dcrc bundle install
[2025-07-07T17:22:05.106Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-07T17:22:05.107Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-07-07T17:22:05.107Z] Executed command in dev-bash: source bin/setenv development
[2025-07-07T17:22:05.107Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-07T17:22:05.107Z] Found existing terminal with given terminal name: alu-dev-env, Skipping creation..
[2025-07-07T17:22:05.107Z] Executed command in alu-dev-env: source bin/setenv development
[2025-07-07T17:22:05.107Z] Executed command in alu-dev-env: dcu alp
[2025-07-07T17:22:05.107Z] Found existing terminal with given terminal name: sandbox-rails console, Skipping creation..
[2025-07-07T17:22:05.108Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-07-07T17:22:05.108Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-07-07T17:22:05.108Z] Persistent terminals creation completed
[2025-07-11T01:52:09.044Z] Extension deactivated
[2025-07-11T17:57:01.520Z] Extension activated
[2025-07-11T17:57:01.521Z] Created terminal: rspec
[2025-07-11T17:57:01.521Z] Executed command in rspec: dcrc bash
[2025-07-11T17:57:01.521Z] Executed command in rspec: bin/rspec
[2025-07-11T17:57:01.522Z] Created terminal: dev-bash
[2025-07-11T17:57:01.522Z] Executed command in dev-bash: dcrc bundle install
[2025-07-11T17:57:01.522Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-11T17:57:01.522Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-07-11T17:57:01.522Z] Executed command in dev-bash: source bin/setenv development
[2025-07-11T17:57:01.522Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-11T17:57:01.522Z] Created terminal: alu-dev-env
[2025-07-11T17:57:01.522Z] Executed command in alu-dev-env: source bin/setenv development
[2025-07-11T17:57:01.522Z] Executed command in alu-dev-env: dcu alp
[2025-07-11T17:57:01.522Z] Created terminal: sandbox-rails console
[2025-07-11T17:57:01.523Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-07-11T17:57:01.523Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-07-11T17:57:01.523Z] Persistent terminals creation completed
